#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration debugging test for 星图超级接口线上化
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_env_file_encoding():
    """Test .env file encoding"""
    print("🔍 Testing .env file encoding...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    try:
        # Try UTF-8 first
        content = env_file.read_text(encoding='utf-8')
        print("✅ .env file readable with UTF-8")
        return True
    except UnicodeDecodeError:
        try:
            # Try GBK
            content = env_file.read_text(encoding='gbk')
            print("⚠️  .env file uses GBK encoding, converting to UTF-8...")
            
            # Write back as UTF-8
            env_file.write_text(content, encoding='utf-8')
            print("✅ Converted .env to UTF-8")
            return True
        except Exception as e:
            print(f"❌ Failed to read .env file: {e}")
            return False


def test_pydantic_settings():
    """Test Pydantic Settings configuration"""
    print("🔍 Testing Pydantic Settings...")
    
    try:
        from pydantic_settings import BaseSettings
        from pydantic import Field
        
        # Test basic settings class
        class TestSettings(BaseSettings):
            test_var: str = Field(default="test", env="TEST_VAR")
            
            class Config:
                env_file = ".env"
                env_file_encoding = "utf-8"
                case_sensitive = False
                extra = "allow"  # Allow extra fields
        
        settings = TestSettings()
        print("✅ Basic Pydantic Settings working")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic Settings error: {e}")
        return False


def test_config_structure():
    """Test configuration structure"""
    print("🔍 Testing configuration structure...")
    
    try:
        # Test individual config classes
        from pydantic_settings import BaseSettings
        from pydantic import Field
        
        class CookieCloudConfig(BaseSettings):
            server_url: str = Field(default="http://localhost:8088", env="COOKIECLOUD_SERVER_URL")
            uuid: str = Field(default="test-uuid", env="COOKIECLOUD_UUID")
            password: str = Field(default="test-password", env="COOKIECLOUD_PASSWORD")
            
            class Config:
                env_file = ".env"
                env_file_encoding = "utf-8"
                extra = "allow"
        
        config = CookieCloudConfig()
        print(f"✅ CookieCloud config: {config.server_url}")
        return True
        
    except Exception as e:
        print(f"❌ Config structure error: {e}")
        return False


def fix_settings_file():
    """Fix the settings.py file"""
    print("🔧 Fixing settings.py file...")
    
    settings_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration settings for 星图超级接口线上化
"""

import os
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pathlib import Path


class CookieCloudConfig(BaseSettings):
    """CookieCloud configuration"""
    server_url: str = Field(default="http://localhost:8088", env="COOKIECLOUD_SERVER_URL")
    uuid: str = Field(default="your-uuid-here", env="COOKIECLOUD_UUID")
    password: str = Field(default="your-password-here", env="COOKIECLOUD_PASSWORD")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class XingtuConfig(BaseSettings):
    """Xingtu API configuration"""
    base_url: str = Field(default="https://www.xingtu.cn", env="XINGTU_BASE_URL")
    domain: str = Field(default="www.xingtu.cn", env="XINGTU_DOMAIN")
    timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_interval: int = Field(default=2, env="RETRY_INTERVAL")
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class AppConfig(BaseSettings):
    """Application configuration"""
    env: str = Field(default="development", env="APP_ENV")
    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    cors_origins: str = Field(default="*", env="CORS_ORIGINS")
    enable_request_logging: bool = Field(default=True, env="ENABLE_REQUEST_LOGGING")

    @validator("cors_origins")
    def parse_cors_origins(cls, v):
        if v == "*":
            return ["*"]
        return [origin.strip() for origin in v.split(",")]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class ExportConfig(BaseSettings):
    """Export configuration"""
    export_dir: str = Field(default="exports", env="EXPORT_DIR")
    max_export_size_mb: int = Field(default=100, env="MAX_EXPORT_SIZE_MB")
    excel_format: str = Field(default="xlsx", env="EXCEL_FORMAT")

    @validator("export_dir")
    def create_export_dir(cls, v):
        Path(v).mkdir(parents=True, exist_ok=True)
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration"""
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    cookie_refresh_interval: int = Field(default=30, env="COOKIE_REFRESH_INTERVAL")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class Settings:
    """Main application settings"""
    
    def __init__(self, **kwargs):
        self.cookiecloud = CookieCloudConfig()
        self.xingtu = XingtuConfig()
        self.app = AppConfig()
        self.export = ExportConfig()
        self.monitoring = MonitoringConfig()


# Global settings instance
settings = Settings()


# Validate required environment variables
def validate_settings():
    """Validate that all required settings are present"""
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID", 
        "COOKIECLOUD_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True


# Common HTTP headers for Xingtu requests
XINGTU_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8'
}
'''
    
    try:
        with open("config/settings.py", "w", encoding="utf-8") as f:
            f.write(settings_content)
        print("✅ Fixed settings.py file")
        return True
    except Exception as e:
        print(f"❌ Failed to fix settings.py: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Debugging configuration issues...")
    print("=" * 50)
    
    # Test encoding
    if not test_env_file_encoding():
        return False
    
    # Test Pydantic Settings
    if not test_pydantic_settings():
        return False
    
    # Test config structure
    if not test_config_structure():
        return False
    
    # Fix settings file
    if not fix_settings_file():
        return False
    
    print("\n✅ All configuration issues fixed!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
