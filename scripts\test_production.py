#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production testing script for 星图超级接口线上化
"""

import asyncio
import httpx
import time
import json
import os
from pathlib import Path


class ProductionTester:
    """Production environment tester"""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.test_author_id = os.getenv("TEST_AUTHOR_ID", "1798563067188323")
        self.results = []
    
    def _get_headers(self):
        """Get request headers"""
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["X-API-Key"] = self.api_key
        return headers
    
    async def test_health_check(self):
        """Test health check endpoint"""
        print("🔍 Testing health check...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health", timeout=30)
                
                result = {
                    "test": "health_check",
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 503],
                    "response_time": response.elapsed.total_seconds(),
                    "details": response.json() if response.status_code in [200, 503] else None
                }
                
                if result["success"]:
                    print(f"✅ Health check: {response.status_code}")
                    if response.status_code == 503:
                        print("⚠️  Service unhealthy - check cookie configuration")
                else:
                    print(f"❌ Health check failed: {response.status_code}")
                
                self.results.append(result)
                return result
                
            except Exception as e:
                print(f"❌ Health check error: {e}")
                result = {"test": "health_check", "success": False, "error": str(e)}
                self.results.append(result)
                return result
    
    async def test_api_info(self):
        """Test API info endpoints"""
        print("🔍 Testing API info endpoints...")
        
        endpoints = ["/", "/endpoints"]
        
        async with httpx.AsyncClient() as client:
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{self.base_url}{endpoint}", timeout=30)
                    
                    result = {
                        "test": f"api_info_{endpoint.replace('/', '_')}",
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response_time": response.elapsed.total_seconds()
                    }
                    
                    if result["success"]:
                        print(f"✅ {endpoint}: {response.status_code}")
                    else:
                        print(f"❌ {endpoint}: {response.status_code}")
                    
                    self.results.append(result)
                    
                except Exception as e:
                    print(f"❌ {endpoint} error: {e}")
                    result = {"test": f"api_info_{endpoint}", "success": False, "error": str(e)}
                    self.results.append(result)
    
    async def test_author_endpoint(self):
        """Test author data endpoint"""
        print("🔍 Testing author data endpoint...")
        
        async with httpx.AsyncClient() as client:
            try:
                # Test basic author info
                response = await client.get(
                    f"{self.base_url}/author/{self.test_author_id}?category=basic",
                    headers=self._get_headers(),
                    timeout=60
                )
                
                result = {
                    "test": "author_endpoint",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if result["success"]:
                    data = response.json()
                    metadata = data.get("_metadata", {})
                    result["author_id"] = metadata.get("author_id")
                    result["success_rate"] = metadata.get("success_rate", 0)
                    result["total_endpoints"] = metadata.get("total_endpoints", 0)
                    
                    print(f"✅ Author endpoint: {response.status_code}")
                    print(f"   Success rate: {result['success_rate']:.2%}")
                    print(f"   Endpoints: {result['total_endpoints']}")
                else:
                    print(f"❌ Author endpoint: {response.status_code}")
                    if response.status_code == 500:
                        print("   Likely cookie authentication issue")
                
                self.results.append(result)
                return result
                
            except Exception as e:
                print(f"❌ Author endpoint error: {e}")
                result = {"test": "author_endpoint", "success": False, "error": str(e)}
                self.results.append(result)
                return result
    
    async def test_export_endpoint(self):
        """Test export functionality"""
        print("🔍 Testing export endpoint...")
        
        async with httpx.AsyncClient() as client:
            try:
                export_data = {
                    "author_id": self.test_author_id,
                    "format": "json",
                    "include_summary": True
                }
                
                response = await client.post(
                    f"{self.base_url}/export",
                    json=export_data,
                    headers=self._get_headers(),
                    timeout=120
                )
                
                result = {
                    "test": "export_endpoint",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds(),
                    "response_size": len(response.content)
                }
                
                if result["success"]:
                    print(f"✅ Export endpoint: {response.status_code}")
                    print(f"   Response size: {result['response_size']} bytes")
                else:
                    print(f"❌ Export endpoint: {response.status_code}")
                
                self.results.append(result)
                return result
                
            except Exception as e:
                print(f"❌ Export endpoint error: {e}")
                result = {"test": "export_endpoint", "success": False, "error": str(e)}
                self.results.append(result)
                return result
    
    async def test_cookie_management(self):
        """Test cookie management endpoints"""
        print("🔍 Testing cookie management...")
        
        async with httpx.AsyncClient() as client:
            try:
                # Test cookie info
                response = await client.get(
                    f"{self.base_url}/cookies/info",
                    headers=self._get_headers(),
                    timeout=30
                )
                
                result = {
                    "test": "cookie_info",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if result["success"]:
                    data = response.json()
                    result["total_cookies"] = data.get("total_cookies", 0)
                    result["xingtu_cookies"] = data.get("xingtu_cookies", 0)
                    
                    print(f"✅ Cookie info: {response.status_code}")
                    print(f"   Total cookies: {result['total_cookies']}")
                    print(f"   Xingtu cookies: {result['xingtu_cookies']}")
                else:
                    print(f"❌ Cookie info: {response.status_code}")
                
                self.results.append(result)
                return result
                
            except Exception as e:
                print(f"❌ Cookie management error: {e}")
                result = {"test": "cookie_info", "success": False, "error": str(e)}
                self.results.append(result)
                return result
    
    async def test_performance(self):
        """Test performance with multiple requests"""
        print("🔍 Testing performance...")
        
        async with httpx.AsyncClient() as client:
            try:
                # Make multiple concurrent requests
                tasks = []
                for i in range(3):
                    task = client.get(
                        f"{self.base_url}/author/{self.test_author_id}?endpoints=base_info",
                        headers=self._get_headers(),
                        timeout=60
                    )
                    tasks.append(task)
                
                start_time = time.time()
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time
                
                successful = 0
                failed = 0
                response_times = []
                
                for response in responses:
                    if isinstance(response, Exception):
                        failed += 1
                    else:
                        if response.status_code == 200:
                            successful += 1
                        else:
                            failed += 1
                        response_times.append(response.elapsed.total_seconds())
                
                result = {
                    "test": "performance",
                    "total_requests": len(tasks),
                    "successful": successful,
                    "failed": failed,
                    "total_time": total_time,
                    "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                    "success": successful > 0
                }
                
                print(f"✅ Performance test completed")
                print(f"   Successful: {successful}/{len(tasks)}")
                print(f"   Total time: {total_time:.2f}s")
                print(f"   Avg response time: {result['avg_response_time']:.2f}s")
                
                self.results.append(result)
                return result
                
            except Exception as e:
                print(f"❌ Performance test error: {e}")
                result = {"test": "performance", "success": False, "error": str(e)}
                self.results.append(result)
                return result
    
    async def run_all_tests(self):
        """Run all production tests"""
        print("🚀 Starting production tests...")
        print("=" * 50)
        
        # Run tests in sequence
        await self.test_health_check()
        await self.test_api_info()
        await self.test_author_endpoint()
        await self.test_export_endpoint()
        await self.test_cookie_management()
        await self.test_performance()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.get("success", False))
        
        print(f"Total tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success rate: {successful_tests / total_tests:.2%}")
        
        print("\n📋 Test Details:")
        for result in self.results:
            status = "✅" if result.get("success", False) else "❌"
            test_name = result.get("test", "unknown")
            print(f"{status} {test_name}")
            
            if "response_time" in result:
                print(f"   Response time: {result['response_time']:.2f}s")
            
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        # Save results to file
        results_file = Path("test_results.json")
        with open(results_file, "w") as f:
            json.dump({
                "timestamp": time.time(),
                "summary": {
                    "total_tests": total_tests,
                    "successful_tests": successful_tests,
                    "success_rate": successful_tests / total_tests
                },
                "results": self.results
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        if successful_tests == total_tests:
            print("\n🎉 All tests passed! Production deployment ready.")
        else:
            print("\n⚠️  Some tests failed. Please review before production deployment.")


async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Production testing for 星图超级接口线上化")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--api-key", help="API key for authentication")
    parser.add_argument("--author-id", help="Test author ID")
    
    args = parser.parse_args()
    
    # Set test author ID if provided
    if args.author_id:
        os.environ["TEST_AUTHOR_ID"] = args.author_id
    
    # Create tester and run tests
    tester = ProductionTester(args.url, args.api_key)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
