#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API integration tests for 星图超级接口线上化
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from config.settings import settings
from core.cookie_manager import Cookie<PERSON>anager
from core.xingtu_client import XingtuClient


class TestAPI:
    """Test suite for API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)
    
    @pytest.fixture
    def sample_author_id(self):
        """Sample author ID for testing"""
        return "1798563067188323"  # Replace with actual test author ID
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert data["status"] == "online"
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code in [200, 503]  # 503 if cookies not configured
        data = response.json()
        assert "status" in data
        assert "components" in data
    
    def test_endpoints_info(self, client):
        """Test endpoints information endpoint"""
        response = client.get("/endpoints")
        assert response.status_code == 200
        data = response.json()
        assert "total_endpoints" in data
        assert "categories" in data
        assert "endpoints" in data
        assert data["total_endpoints"] > 0
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_author_info_endpoint(self, client, sample_author_id):
        """Test author info endpoint with real data"""
        response = client.get(f"/author/{sample_author_id}")
        
        # Should return 200 if cookies are valid, or 500 if not
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "_metadata" in data
            assert data["_metadata"]["author_id"] == sample_author_id
    
    def test_author_info_invalid_id(self, client):
        """Test author info endpoint with invalid ID"""
        response = client.get("/author/invalid_id")
        assert response.status_code == 400
        data = response.json()
        assert "Invalid author ID format" in data["detail"]
    
    def test_author_info_with_category(self, client, sample_author_id):
        """Test author info endpoint with category filter"""
        response = client.get(f"/author/{sample_author_id}?category=basic")
        # Should return 200 or 500 depending on cookie availability
        assert response.status_code in [200, 500]
    
    def test_author_info_with_endpoints(self, client, sample_author_id):
        """Test author info endpoint with specific endpoints"""
        response = client.get(f"/author/{sample_author_id}?endpoints=base_info,global_info")
        # Should return 200 or 500 depending on cookie availability
        assert response.status_code in [200, 500]
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_export_endpoint(self, client, sample_author_id):
        """Test export endpoint"""
        export_data = {
            "author_id": sample_author_id,
            "format": "json",
            "include_raw_data": True,
            "include_summary": True
        }
        
        response = client.post("/export", json=export_data)
        # Should return 200 if successful, or 500 if cookies invalid
        assert response.status_code in [200, 500]
    
    def test_export_invalid_format(self, client, sample_author_id):
        """Test export endpoint with invalid format"""
        export_data = {
            "author_id": sample_author_id,
            "format": "invalid_format"
        }
        
        response = client.post("/export", json=export_data)
        assert response.status_code == 400
        data = response.json()
        assert "Unsupported export format" in data["detail"]
    
    def test_batch_endpoint_invalid_size(self, client):
        """Test batch endpoint with too many author IDs"""
        batch_data = {
            "author_ids": ["123"] * 101,  # Exceed limit
            "format": "json"
        }
        
        response = client.post("/batch", json=batch_data)
        assert response.status_code == 400
        data = response.json()
        assert "Batch size too large" in data["detail"]
    
    def test_batch_endpoint_invalid_ids(self, client):
        """Test batch endpoint with invalid author IDs"""
        batch_data = {
            "author_ids": ["invalid_id", "another_invalid"],
            "format": "json"
        }
        
        response = client.post("/batch", json=batch_data)
        assert response.status_code == 400
        data = response.json()
        assert "Invalid author IDs" in data["detail"]
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_cookie_refresh_endpoint(self, client):
        """Test cookie refresh endpoint"""
        response = client.post("/cookies/refresh")
        # Should return 200 if successful, or 500 if CookieCloud unavailable
        assert response.status_code in [200, 500]
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_cookie_info_endpoint(self, client):
        """Test cookie info endpoint"""
        response = client.get("/cookies/info")
        # Should return 200 if successful, or 500 if CookieCloud unavailable
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "total_domains" in data
            assert "total_cookies" in data


class TestCookieManager:
    """Test suite for CookieManager"""
    
    @pytest.fixture
    def cookie_manager(self):
        """CookieManager fixture"""
        return CookieManager()
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_load_cookies(self, cookie_manager):
        """Test cookie loading"""
        result = cookie_manager.load_cookies()
        assert isinstance(result, bool)
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    def test_get_xingtu_cookies(self, cookie_manager):
        """Test getting Xingtu cookies"""
        cookies = cookie_manager.get_xingtu_cookies()
        assert isinstance(cookies, dict)
    
    def test_get_cookies_info(self, cookie_manager):
        """Test getting cookie information"""
        info = cookie_manager.get_cookies_info()
        assert isinstance(info, dict)
        assert "total_domains" in info
        assert "total_cookies" in info


class TestXingtuClient:
    """Test suite for XingtuClient"""
    
    @pytest.fixture
    def xingtu_client(self):
        """XingtuClient fixture"""
        cookie_manager = CookieManager()
        return XingtuClient(cookie_manager)
    
    def test_get_available_endpoints(self, xingtu_client):
        """Test getting available endpoints"""
        endpoints = xingtu_client.get_available_endpoints()
        assert isinstance(endpoints, list)
        assert len(endpoints) > 0
        
        # Check endpoint structure
        for endpoint in endpoints:
            assert "name" in endpoint
            assert "path" in endpoint
            assert "category" in endpoint
    
    def test_get_health_status(self, xingtu_client):
        """Test getting health status"""
        status = xingtu_client.get_health_status()
        assert isinstance(status, dict)
        assert "session_ready" in status
        assert "total_endpoints" in status
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    async def test_get_author_info(self, xingtu_client):
        """Test getting author info"""
        author_id = "1798563067188323"  # Replace with actual test author ID
        result = await xingtu_client.get_author_info(author_id, ["base_info"])
        
        assert isinstance(result, dict)
        assert "_metadata" in result
        assert result["_metadata"]["author_id"] == author_id


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
