#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data validation utilities for 星图超级接口线上化
"""

import re
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import pandas as pd


def validate_author_id(author_id: str) -> bool:
    """
    Validate Xingtu author ID format
    
    Args:
        author_id: Author ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not author_id:
        return False
    
    # Author ID should be numeric string
    return author_id.isdigit() and len(author_id) > 0


def validate_date_range(start_date: str, end_date: str) -> bool:
    """
    Validate date range format and logic
    
    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        
    Returns:
        True if valid, False otherwise
    """
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        return start <= end
    except ValueError:
        return False


def clean_text(text: Any) -> str:
    """
    Clean and normalize text data
    
    Args:
        text: Text to clean
        
    Returns:
        Cleaned text string
    """
    if pd.isna(text) or text is None:
        return ""
    
    if not isinstance(text, str):
        text = str(text)
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove control characters
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    return text


def validate_json_response(response: Dict[str, Any]) -> bool:
    """
    Validate Xingtu API response format
    
    Args:
        response: API response dictionary
        
    Returns:
        True if valid response format
    """
    if not isinstance(response, dict):
        return False
    
    # Check for common success indicators
    if "code" in response:
        return response.get("code") in [0, "0"]
    
    if "status_code" in response:
        return response.get("status_code") in [0, "0"]
    
    # If no explicit status, assume valid if has data
    return "data" in response or len(response) > 0


def validate_export_data(data: List[Dict[str, Any]]) -> bool:
    """
    Validate data before export
    
    Args:
        data: List of records to validate
        
    Returns:
        True if data is valid for export
    """
    if not isinstance(data, list):
        return False
    
    if len(data) == 0:
        return False
    
    # Check that all items are dictionaries
    return all(isinstance(item, dict) for item in data)


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file system usage
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove control characters
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    
    # Ensure not empty
    if not filename.strip():
        filename = "export"
    
    return filename.strip()


def validate_cookie_format(cookies: Dict[str, str]) -> bool:
    """
    Validate cookie dictionary format
    
    Args:
        cookies: Cookie dictionary
        
    Returns:
        True if valid cookie format
    """
    if not isinstance(cookies, dict):
        return False
    
    # Check that all keys and values are strings
    for key, value in cookies.items():
        if not isinstance(key, str) or not isinstance(value, str):
            return False
    
    return True


def validate_api_endpoint_params(params: Dict[str, Any]) -> bool:
    """
    Validate API endpoint parameters
    
    Args:
        params: Parameters dictionary
        
    Returns:
        True if valid parameters
    """
    if not isinstance(params, dict):
        return False
    
    # Check for required author ID parameter
    author_id_params = ["o_author_id", "author_id", "star_author_id"]
    has_author_id = any(param in params for param in author_id_params)
    
    if not has_author_id:
        return False
    
    # Validate author ID if present
    for param in author_id_params:
        if param in params:
            if not validate_author_id(str(params[param])):
                return False
    
    return True


def validate_export_format(format_type: str) -> bool:
    """
    Validate export format type
    
    Args:
        format_type: Export format (json, excel, csv)
        
    Returns:
        True if valid format
    """
    valid_formats = ["json", "excel", "xlsx", "csv"]
    return format_type.lower() in valid_formats


def normalize_api_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normalize API response format
    
    Args:
        response: Raw API response
        
    Returns:
        Normalized response
    """
    if not isinstance(response, dict):
        return {"error": "Invalid response format", "data": None}
    
    # Extract data based on common patterns
    if "data" in response:
        return {
            "success": True,
            "data": response["data"],
            "code": response.get("code", 0),
            "message": response.get("message", "Success")
        }
    
    # If no explicit data field, use entire response as data
    return {
        "success": True,
        "data": response,
        "code": 0,
        "message": "Success"
    }


def validate_rate_limit_params(requests_per_minute: int) -> bool:
    """
    Validate rate limiting parameters
    
    Args:
        requests_per_minute: Number of requests per minute
        
    Returns:
        True if valid rate limit
    """
    return isinstance(requests_per_minute, int) and 1 <= requests_per_minute <= 1000


def flatten_nested_dict(data: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
    """
    Flatten nested dictionary for Excel export
    
    Args:
        data: Nested dictionary
        parent_key: Parent key prefix
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items = []
    
    for key, value in data.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key
        
        if isinstance(value, dict):
            items.extend(flatten_nested_dict(value, new_key, sep=sep).items())
        elif isinstance(value, list):
            # Convert list to string representation
            items.append((new_key, json.dumps(value, ensure_ascii=False)))
        else:
            items.append((new_key, value))
    
    return dict(items)


def validate_environment_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate environment configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        List of validation errors (empty if valid)
    """
    errors = []
    
    # Required fields
    required_fields = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID",
        "COOKIECLOUD_PASSWORD"
    ]
    
    for field in required_fields:
        if field not in config or not config[field]:
            errors.append(f"Missing required field: {field}")
    
    # Validate URL format
    if "COOKIECLOUD_SERVER_URL" in config:
        url = config["COOKIECLOUD_SERVER_URL"]
        if not re.match(r'^https?://', url):
            errors.append("COOKIECLOUD_SERVER_URL must be a valid HTTP/HTTPS URL")
    
    # Validate numeric fields
    numeric_fields = ["REQUEST_TIMEOUT", "MAX_RETRIES", "API_PORT"]
    for field in numeric_fields:
        if field in config:
            try:
                int(config[field])
            except (ValueError, TypeError):
                errors.append(f"{field} must be a valid integer")
    
    return errors
