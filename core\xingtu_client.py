#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Xingtu API client with dynamic cookie management
"""

import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import structlog

from config.settings import settings, XINGTU_HEADERS
from config.api_endpoints import XINGTU_API_ENDPOINTS, APIEndpoint
from core.cookie_manager import <PERSON>ie<PERSON>anager
from utils.logger import log_api_request, log_api_response, log_error
from utils.validators import validate_author_id, validate_json_response, normalize_api_response


class RateLimiter:
    """Simple rate limiter for API requests"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests = []
    
    async def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        now = time.time()
        
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]
        
        # Check if we need to wait
        if len(self.requests) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.requests[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        # Record this request
        self.requests.append(now)


class XingtuClient:
    """
    Xingtu API client with dynamic cookie management and robust error handling
    """
    
    def __init__(self, cookie_manager: CookieManager):
        """
        Initialize Xingtu client
        
        Args:
            cookie_manager: CookieManager instance for authentication
        """
        self.logger = structlog.get_logger("xingtu_client")
        self.config = settings.xingtu
        self.cookie_manager = cookie_manager
        self.session = None
        self.rate_limiter = RateLimiter(self.config.rate_limit_per_minute)
        self._setup_session()
    
    def _setup_session(self):
        """Setup requests session with retry strategy and timeouts"""
        self.session = requests.Session()
        
        # Setup retry strategy
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_interval,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update(XINGTU_HEADERS)
        
        # Disable SSL verification warnings
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        self.session.verify = False
        
        self.logger.info("Xingtu session setup completed")
    
    def _update_session_cookies(self):
        """Update session with fresh cookies"""
        try:
            cookies = self.cookie_manager.get_xingtu_cookies()
            if not cookies:
                self.logger.warning("No Xingtu cookies available")
                return False
            
            # Update session cookies
            self.session.cookies.clear()
            self.session.cookies.update(cookies)
            
            # Update CSRF token in headers
            csrf_token = self.cookie_manager.get_csrf_token()
            if csrf_token:
                self.session.headers['x-csrftoken'] = csrf_token
            
            # Update cookie header
            cookie_header = self.cookie_manager.get_cookie_header()
            if cookie_header:
                self.session.headers['Cookie'] = cookie_header
            
            self.logger.debug("Session cookies updated", cookie_count=len(cookies))
            return True
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "update_session_cookies"})
            return False
    
    async def _make_request(self, endpoint: APIEndpoint, author_id: str) -> Dict[str, Any]:
        """
        Make API request to specific endpoint
        
        Args:
            endpoint: API endpoint configuration
            author_id: Author ID for the request
            
        Returns:
            API response data
        """
        # Rate limiting
        await self.rate_limiter.wait_if_needed()
        
        # Update cookies before request
        if not self._update_session_cookies():
            return {"error": "Failed to update session cookies", "data": None}
        
        # Prepare URL and parameters
        url = self.config.base_url + endpoint.path
        params = endpoint.specific_params.copy()
        params[endpoint.id_param_name] = author_id
        
        start_time = time.time()
        
        try:
            log_api_request(self.logger, "GET", url, params)
            
            response = self.session.get(
                url,
                params=params,
                timeout=self.config.timeout
            )
            
            response_time = time.time() - start_time
            response_size = len(response.content) if response.content else 0
            
            log_api_response(self.logger, response.status_code, response_time, response_size)
            
            # Handle response
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if validate_json_response(data):
                        return normalize_api_response(data)
                    else:
                        return {
                            "error": f"API Error: {data.get('message', 'Unknown error')}",
                            "data": None,
                            "raw_response": data
                        }
                        
                except ValueError as e:
                    return {
                        "error": f"JSON decode error: {str(e)}",
                        "data": None,
                        "content_type": response.headers.get('Content-Type', 'unknown')
                    }
            else:
                return {
                    "error": f"HTTP {response.status_code}: {response.text[:500]}",
                    "data": None
                }
                
        except requests.exceptions.Timeout:
            return {"error": f"Request timeout after {self.config.timeout}s", "data": None}
        except requests.exceptions.RequestException as e:
            log_error(self.logger, e, {"endpoint": endpoint.name, "author_id": author_id})
            return {"error": f"Request failed: {str(e)}", "data": None}
        except Exception as e:
            log_error(self.logger, e, {"endpoint": endpoint.name, "author_id": author_id})
            return {"error": f"Unexpected error: {str(e)}", "data": None}
    
    async def get_author_info(self, author_id: str, 
                            endpoints: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get comprehensive author information
        
        Args:
            author_id: Xingtu author ID
            endpoints: Specific endpoints to fetch (None for all)
            
        Returns:
            Dictionary with author data from all endpoints
        """
        if not validate_author_id(author_id):
            return {"error": "Invalid author ID format", "data": None}
        
        # Validate cookies before starting
        if not self.cookie_manager.validate_xingtu_cookies():
            # Try to refresh cookies
            if not self.cookie_manager.refresh_cookies():
                return {"error": "Cookie validation failed", "data": None}
        
        self.logger.info("Fetching author information", author_id=author_id)
        
        # Determine which endpoints to use
        if endpoints:
            endpoint_list = [ep for ep in XINGTU_API_ENDPOINTS if ep.name in endpoints]
        else:
            endpoint_list = XINGTU_API_ENDPOINTS
        
        author_data = {}
        successful_requests = 0
        failed_requests = 0
        
        # Process endpoints
        for endpoint in endpoint_list:
            self.logger.debug("Fetching endpoint", endpoint=endpoint.name)
            
            try:
                result = await self._make_request(endpoint, author_id)
                author_data[endpoint.name] = result
                
                if result.get("success", False):
                    successful_requests += 1
                else:
                    failed_requests += 1
                    self.logger.warning(
                        "Endpoint failed", 
                        endpoint=endpoint.name, 
                        error=result.get("error", "Unknown error")
                    )
                    
            except Exception as e:
                log_error(self.logger, e, {"endpoint": endpoint.name, "author_id": author_id})
                author_data[endpoint.name] = {"error": f"Processing failed: {str(e)}", "data": None}
                failed_requests += 1
        
        # Add metadata
        author_data["_metadata"] = {
            "author_id": author_id,
            "total_endpoints": len(endpoint_list),
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "fetch_timestamp": time.time(),
            "success_rate": successful_requests / len(endpoint_list) if endpoint_list else 0
        }
        
        self.logger.info(
            "Author info fetch completed",
            author_id=author_id,
            successful=successful_requests,
            failed=failed_requests,
            success_rate=f"{(successful_requests / len(endpoint_list) * 100):.1f}%" if endpoint_list else "0%"
        )
        
        return author_data
    
    async def get_author_info_by_category(self, author_id: str, 
                                        category: str) -> Dict[str, Any]:
        """
        Get author information for specific category
        
        Args:
            author_id: Xingtu author ID
            category: Endpoint category (basic, commerce, analytics, etc.)
            
        Returns:
            Dictionary with author data from category endpoints
        """
        category_endpoints = [ep.name for ep in XINGTU_API_ENDPOINTS if ep.category == category]
        
        if not category_endpoints:
            return {"error": f"No endpoints found for category: {category}", "data": None}
        
        return await self.get_author_info(author_id, category_endpoints)
    
    def get_available_endpoints(self) -> List[Dict[str, str]]:
        """
        Get list of available API endpoints
        
        Returns:
            List of endpoint information
        """
        return [
            {
                "name": ep.name,
                "path": ep.path,
                "category": ep.category,
                "description": ep.description,
                "id_param": ep.id_param_name
            }
            for ep in XINGTU_API_ENDPOINTS
        ]
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get client health status
        
        Returns:
            Health status information
        """
        cookie_info = self.cookie_manager.get_cookies_info()
        
        return {
            "session_ready": self.session is not None,
            "cookies_available": cookie_info["xingtu_cookies"] > 0,
            "csrf_token_available": cookie_info["csrf_token_available"],
            "last_cookie_refresh": cookie_info["last_refresh"],
            "total_endpoints": len(XINGTU_API_ENDPOINTS),
            "rate_limit": self.config.rate_limit_per_minute,
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries
        }
