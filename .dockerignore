# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
exports/
data/
*.xlsx
*.csv
*.json

# Environment files
.env
.env.local
.env.*.local

# Documentation
README.md
docs/

# Tests
tests/
pytest.ini
.pytest_cache/

# Development tools
.flake8
.black
.isort.cfg
mypy.ini

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Nginx
nginx/
