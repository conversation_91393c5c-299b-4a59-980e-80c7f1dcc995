import requests
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
import warnings
import urllib3

# Ignore InsecureRequestWarning: Unverified HTTPS request
warnings.simplefilter('ignore', urllib3.exceptions.InsecureRequestWarning)
warnings.simplefilter('ignore', requests.packages.urllib3.exceptions.InsecureRequestWarning)


app = FastAPI(title="Xingtu Author Info API")

# --- Configuration ---
BASE_URL = "https://www.xingtu.cn"

# Hardcoded Cookie and CSRF Token from the provided cURL example
COOKIE = "s_v_web_id=verify_m9qxjubu_HLVFes74_oVPh_42lW_Ae85_WjD5hF136EVd; csrf_session_id=1111e0d59d2dbded2c7ff8d0ed7010c8; tt_webid=7495710578595333659; csrftoken=3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I; passport_csrf_token=24e5abbc2ac713c15a146cf11328edef; passport_csrf_token_default=24e5abbc2ac713c15a146cf11328edef; passport_auth_status=f54ae30d80da739a44705016bd08cacc%2C; passport_auth_status_ss=f54ae30d80da739a44705016bd08cacc%2C; sid_guard=d17823b9568a5e4d638054f07a24b406%7C1745231145%7C5184001%7CFri%2C+20-Jun-2025+10%3A25%3A46+GMT; uid_tt=b872dc5d9797ca9d395ada8525b9d668; uid_tt_ss=b872dc5d9797ca9d395ada8525b9d668; sid_tt=d17823b9568a5e4d638054f07a24b406; sessionid=d17823b9568a5e4d638054f07a24b406; sessionid_ss=d17823b9568a5e4d638054f07a24b406; is_staff_user=false; sid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; ssid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; star_sessionid=d17823b9568a5e4d638054f07a24b406; possess_scene_star_id=1798563067188323"
CSRF_TOKEN = "3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I" # Extracted from headers

# Common headers based on cURL
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'x-csrftoken': CSRF_TOKEN,
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/', # Using a simpler base referer
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
    'Cookie': COOKIE
}

# Define the API endpoints to fetch data from
# Each entry contains: name (for key in result), path, specific_params, id_param_name
API_ENDPOINTS = [
    {"name": "commerce_seed_base_info", "path": "/gw/api/aggregator/get_author_commerce_seed_base_info", "specific_params": {"range": "30"}, "id_param_name": "o_author_id"},
    {"name": "commerce_spread_info", "path": "/gw/api/aggregator/get_author_commerce_spread_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "content_label_density", "path": "/gw/api/aggregator/get_author_content_label_density", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "contract_base_info", "path": "/gw/api/aggregator/get_author_contract_base_info", "specific_params": {"range": "90"}, "id_param_name": "o_author_id"},
    {"name": "global_info", "path": "/gw/api/aggregator/get_author_global_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "homepage_videos", "path": "/gw/api/aggregator/get_author_homepage_videos", "specific_params": {"page": "1", "limit": "10"}, "id_param_name": "o_author_id"},
    {"name": "order_experience", "path": "/gw/api/aggregator/get_author_order_experience", "specific_params": {"period": "30"}, "id_param_name": "o_author_id"},
    {"name": "side_base_info", "path": "/gw/api/aggregator/get_author_side_base_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "video_live_linkage_product_list", "path": "/gw/api/aggregator/get_author_video_live_linkage_product_list", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "video_live_linkage_stat", "path": "/gw/api/aggregator/get_author_video_live_linkage_stat", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "base_info", "path": "/gw/api/author/get_author_base_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true"}, "id_param_name": "o_author_id"},
    {"name": "marketing_info", "path": "/gw/api/author/get_author_marketing_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "platform_channel_info_v2", "path": "/gw/api/author/get_author_platform_channel_info_v2", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "show_items_v2", "path": "/gw/api/author/get_author_show_items_v2", "specific_params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "hot_comment_tokens", "path": "/gw/api/data/get_author_hot_comment_tokens", "specific_params": {"num": "10", "without_emoji": "true"}, "id_param_name": "author_id"},
    {"name": "audience_distribution", "path": "/gw/api/data_sp/author_audience_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1", "link_type": "5"}, "id_param_name": "o_author_id"},
    {"name": "cp_info", "path": "/gw/api/data_sp/author_cp_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    # Note: Dates in daily_link are hardcoded as per example, consider making dynamic if needed
    {"name": "daily_link", "path": "/gw/api/data_sp/author_daily_link", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22"}, "id_param_name": "o_author_id"},
    {"name": "link_card", "path": "/gw/api/data_sp/author_link_card", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "link_struct", "path": "/gw/api/data_sp/author_link_struct", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "local_info", "path": "/gw/api/data_sp/author_local_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30"}, "id_param_name": "o_author_id"},
    {"name": "local_sales_performance", "path": "/gw/api/data_sp/author_local_sales_performance", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "shopping_videos", "path": "/gw/api/data_sp/author_shopping_videos", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "spread_videos", "path": "/gw/api/data_sp/author_spread_videos", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "video_distribution", "path": "/gw/api/data_sp/author_video_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "convert_ability", "path": "/gw/api/data_sp/get_author_convert_ability", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2"}, "id_param_name": "o_author_id"},
    # Note: Dates in daily_fans are hardcoded as per example, consider making dynamic if needed
    {"name": "daily_fans", "path": "/gw/api/data_sp/get_author_daily_fans", "specific_params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1"}, "id_param_name": "author_id"},
    {"name": "spread_info_data_sp", "path": "/gw/api/data_sp/get_author_spread_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2"}, "id_param_name": "o_author_id"},
    {"name": "business_card_info", "path": "/gw/api/gauthor/author_get_business_card_info", "specific_params": {}, "id_param_name": "o_author_id"},
]

# Proxies disabled as requested
PROXIES = {
  "http": None,
  "https": None,
}

# --- API Endpoint ---
@app.get("/author/{o_author_id}", summary="Get Comprehensive Author Info by Xingtu ID")
async def get_author_info(o_author_id: str):
    """
    Retrieves comprehensive information for a Xingtu author by their ID
    by calling multiple internal Xingtu APIs.

    - **o_author_id**: The unique identifier for the Xingtu author.
    """
    author_data = {}
    session = requests.Session() # Use a session for potential connection reuse
    session.headers.update(HEADERS)
    session.proxies.update(PROXIES)
    session.verify = False # Disable SSL verification

    print(f"Fetching data for author ID: {o_author_id}")

    for endpoint in API_ENDPOINTS:
        url = BASE_URL + endpoint["path"]
        # Prepare query parameters, adding the correct author ID
        query_params = endpoint["specific_params"].copy()
        query_params[endpoint["id_param_name"]] = o_author_id

        endpoint_name = endpoint["name"]
        print(f"  Fetching {endpoint_name}...")

        try:
            response = session.get(url, params=query_params, timeout=15) # Added timeout
            response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)

            # Check if response content type is JSON before parsing
            content_type = response.headers.get('Content-Type', '')
            if 'application/json' in content_type:
                 data = response.json()
                 if data.get("code") == 0 or data.get("status_code") == 0: # Common success codes in Xingtu API
                     author_data[endpoint_name] = data.get("data", data) # Prefer 'data' field if exists
                 else:
                     # Capture API specific errors if code is not 0
                     author_data[endpoint_name] = {"error": f"API Error: code={data.get('code')}, message={data.get('message', 'N/A')}", "raw_response": data}
                     print(f"    API Error for {endpoint_name}: {data.get('message', 'N/A')} (Code: {data.get('code')})")
            else:
                # Handle non-JSON responses if necessary
                author_data[endpoint_name] = {"error": f"Non-JSON response received. Content-Type: {content_type}", "content": response.text[:500]} # Log snippet
                print(f"    Warning: Non-JSON response for {endpoint_name}. Content-Type: {content_type}")


        except requests.exceptions.RequestException as e:
            print(f"    Error fetching {endpoint_name}: {e}")
            author_data[endpoint_name] = {"error": f"Request failed: {str(e)}"}
        except Exception as e: # Catch other potential errors like JSONDecodeError
            print(f"    Error processing {endpoint_name}: {e}")
            author_data[endpoint_name] = {"error": f"Processing failed: {str(e)}"}

    if not author_data:
         raise HTTPException(status_code=404, detail=f"Could not fetch any data for author ID: {o_author_id}")

    print(f"Finished fetching data for author ID: {o_author_id}")
    return JSONResponse(content=author_data)

# --- Run the App ---
if __name__ == "__main__":
    print("Starting Xingtu Author Info API server...")
    # Use reload=True for development, disable in production
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 