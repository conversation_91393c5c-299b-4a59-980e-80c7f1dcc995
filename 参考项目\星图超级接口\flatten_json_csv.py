#!/usr/bin/env python
"""flatten_json_csv.py

扁平化含 JSON 字段的 CSV 或 Excel 文件。
使用方法：
    python flatten_json_csv.py
    
- 通过文件对话框选择输入和输出文件
- 支持 CSV (.csv) 和 Excel (.xlsx) 文件格式
- 自动检测所有以 '{' 开头的列为 JSON
- 深度递归展开 dict / list，包括 distribution_list、category_items 等常见格式
- 对列表下标保留 [idx] 标记，避免列冲突
- 对 audience_distribution.distributions / 任意 *distribution_list* 按 type_display 合并，不携带 [index]
- 对其他 kv 列表 (category_items 等) 用 name 合并
"""

import sys, os, json, pandas as pd
import tkinter as tk
from tkinter import filedialog

def safe_load(x):
    try:
        return json.loads(x) if pd.notnull(x) else {}
    except Exception:
        return {}

# ---------- 核心递归 ----------
def flatten(obj, path, out):
    if isinstance(obj, dict):
        # 优先处理含 distribution_list
        if 'distribution_list' in obj and isinstance(obj['distribution_list'], list):
            tdisp = obj.get('type_display') or str(obj.get('type', ''))
            for dist in obj['distribution_list']:
                if isinstance(dist, dict) and 'distribution_key' in dist:
                    key = dist['distribution_key']
                    val = dist['distribution_value']
                    col = f'{path}.{tdisp}.{key}' if path else f'{tdisp}.{key}'
                    out[col] = val
        # 含 category_items
        if 'category_items' in obj and isinstance(obj['category_items'], list):
            for item in obj['category_items']:
                if isinstance(item, dict) and 'name' in item:
                    name = item['name']
                    val  = item.get('rate') or item.get('sale_amount_range') or item.get('value')
                    col  = f'{path}.类目.{name}' if path else f'类目.{name}'
                    out[col] = val
        # 递归其余键
        for k, v in obj.items():
            if k in ('distribution_list', 'category_items'):
                continue
            new_path = f'{path}.{k}' if path else k
            flatten(v, new_path, out)
    elif isinstance(obj, list):
        # 如果父路径以 ".distributions" 结尾，则跳过索引（已由 type_display 区分）
        if path.endswith('.distributions'):
            for item in obj:
                flatten(item, path, out)  # 不加索引
        else:
            for idx, item in enumerate(obj):
                flatten(item, f'{path}[{idx}]', out)
    else:
        out[path] = obj

def flatten_series(series: pd.Series, prefix: str) -> pd.DataFrame:
    rows = []
    for txt in series:
        node = safe_load(txt)
        d = {}
        flatten(node, prefix, d)
        rows.append(d)
    return pd.DataFrame(rows)

def read_file(file_path):
    """根据文件扩展名读取文件"""
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.csv':
        return pd.read_csv(file_path)
    elif ext == '.xlsx':
        return pd.read_excel(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {ext}")

def save_file(df, file_path):
    """根据文件扩展名保存文件"""
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.csv':
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
    elif ext == '.xlsx':
        df.to_excel(file_path, index=False)
    else:
        raise ValueError(f"不支持的文件格式: {ext}")

def main():
    # 隐藏主窗口
    root = tk.Tk()
    root.withdraw()
    
    # 选择输入文件
    input_file = filedialog.askopenfilename(
        title="选择包含JSON字段的输入文件",
        filetypes=[("表格文件", "*.csv *.xlsx"), ("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
    )
    
    if not input_file:
        print("未选择输入文件，程序退出")
        return
    
    # 选择输出文件
    default_ext = os.path.splitext(input_file)[1]
    default_name = os.path.splitext(os.path.basename(input_file))[0] + "_扁平化" + default_ext
    output_file = filedialog.asksaveasfilename(
        title="选择扁平化后的输出文件",
        initialfile=default_name,
        defaultextension=default_ext,
        filetypes=[("表格文件", "*.csv *.xlsx"), ("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
    )
    
    if not output_file:
        print("未选择输出文件，程序退出")
        return
    
    try:
        # 读取输入文件
        df = read_file(input_file)
        
        # 处理JSON字段
        json_cols = [c for c in df.columns if df[c].astype(str).str.startswith('{').any()]
        parts = [df[[c for c in df.columns if c not in json_cols]]]  # 非 JSON 列原样保留
        
        for col in json_cols:
            parts.append(flatten_series(df[col], col))
        
        df_flat = pd.concat(parts, axis=1)
        
        # 可选：排序列，易读
        df_flat = df_flat.reindex(sorted(df_flat.columns), axis=1)
        
        # 保存输出文件
        save_file(df_flat, output_file)
        print(f'>> 扁平化文件已保存至: {output_file}  (shape: {df_flat.shape})')
    
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == '__main__':
    main()
