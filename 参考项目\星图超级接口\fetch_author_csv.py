import requests
import argparse
import warnings
import urllib3
import json
import time
import pandas as pd # Import pandas
import tkinter as tk # Import tkinter for GUI
from tkinter import filedialog # Import filedialog separately
import sys # Import sys for exiting
import os # Ensure os is imported
import csv

# Ignore InsecureRequestWarning: Unverified HTTPS request
warnings.simplefilter('ignore', urllib3.exceptions.InsecureRequestWarning)
warnings.simplefilter('ignore', requests.packages.urllib3.exceptions.InsecureRequestWarning)

# --- Configuration ---
BASE_URL = "https://www.xingtu.cn"
COOKIE = "s_v_web_id=verify_m9qxjubu_HLVFes74_oVPh_42lW_Ae85_WjD5hF136EVd; csrf_session_id=1111e0d59d2dbded2c7ff8d0ed7010c8; tt_webid=7495710578595333659; csrftoken=3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I; passport_csrf_token=24e5abbc2ac713c15a146cf11328edef; passport_csrf_token_default=24e5abbc2ac713c15a146cf11328edef; passport_auth_status=f54ae30d80da739a44705016bd08cacc%2C; passport_auth_status_ss=f54ae30d80da739a44705016bd08cacc%2C; sid_guard=d17823b9568a5e4d638054f07a24b406%7C1745231145%7C5184001%7CFri%2C+20-Jun-2025+10%3A25%3A46+GMT; uid_tt=b872dc5d9797ca9d395ada8525b9d668; uid_tt_ss=b872dc5d9797ca9d395ada8525b9d668; sid_tt=d17823b9568a5e4d638054f07a24b406; sessionid=d17823b9568a5e4d638054f07a24b406; sessionid_ss=d17823b9568a5e4d638054f07a24b406; is_staff_user=false; sid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; ssid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; star_sessionid=d17823b9568a5e4d638054f07a24b406; Hm_lvt_5d77c979053345c4bd8db63329f818ec=**********; Hm_lpvt_5d77c979053345c4bd8db63329f818ec=**********; HMACCOUNT=E102D4CB3F9EAFEA"
CSRF_TOKEN = "3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    # Let requests handle Accept-Encoding automatically
    # 'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'x-csrftoken': CSRF_TOKEN,
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
    'Cookie': COOKIE
}
# Restore the full endpoint list
API_ENDPOINTS = [
    {"name": "commerce_seed_base_info", "path": "/gw/api/aggregator/get_author_commerce_seed_base_info", "specific_params": {"range": "30"}, "id_param_name": "o_author_id"},
    {"name": "commerce_spread_info", "path": "/gw/api/aggregator/get_author_commerce_spread_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "content_label_density", "path": "/gw/api/aggregator/get_author_content_label_density", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "contract_base_info", "path": "/gw/api/aggregator/get_author_contract_base_info", "specific_params": {"range": "90"}, "id_param_name": "o_author_id"},
    {"name": "global_info", "path": "/gw/api/aggregator/get_author_global_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "homepage_videos", "path": "/gw/api/aggregator/get_author_homepage_videos", "specific_params": {"page": "1", "limit": "10"}, "id_param_name": "o_author_id"},
    {"name": "order_experience", "path": "/gw/api/aggregator/get_author_order_experience", "specific_params": {"period": "30"}, "id_param_name": "o_author_id"},
    {"name": "side_base_info", "path": "/gw/api/aggregator/get_author_side_base_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "video_live_linkage_product_list", "path": "/gw/api/aggregator/get_author_video_live_linkage_product_list", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "video_live_linkage_stat", "path": "/gw/api/aggregator/get_author_video_live_linkage_stat", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "base_info", "path": "/gw/api/author/get_author_base_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true"}, "id_param_name": "o_author_id"},
    {"name": "marketing_info", "path": "/gw/api/author/get_author_marketing_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "platform_channel_info_v2", "path": "/gw/api/author/get_author_platform_channel_info_v2", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "show_items_v2", "path": "/gw/api/author/get_author_show_items_v2", "specific_params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "hot_comment_tokens", "path": "/gw/api/data/get_author_hot_comment_tokens", "specific_params": {"num": "10", "without_emoji": "true"}, "id_param_name": "author_id"},
    {"name": "audience_distribution", "path": "/gw/api/data_sp/author_audience_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1", "link_type": "5"}, "id_param_name": "o_author_id"},
    {"name": "cp_info", "path": "/gw/api/data_sp/author_cp_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "daily_link", "path": "/gw/api/data_sp/author_daily_link", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22"}, "id_param_name": "o_author_id"},
    {"name": "link_card", "path": "/gw/api/data_sp/author_link_card", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "link_struct", "path": "/gw/api/data_sp/author_link_struct", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "local_info", "path": "/gw/api/data_sp/author_local_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30"}, "id_param_name": "o_author_id"},
    {"name": "local_sales_performance", "path": "/gw/api/data_sp/author_local_sales_performance", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "shopping_videos", "path": "/gw/api/data_sp/author_shopping_videos", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "spread_videos", "path": "/gw/api/data_sp/author_spread_videos", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "video_distribution", "path": "/gw/api/data_sp/author_video_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "convert_ability", "path": "/gw/api/data_sp/get_author_convert_ability", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2"}, "id_param_name": "o_author_id"},
    {"name": "daily_fans", "path": "/gw/api/data_sp/get_author_daily_fans", "specific_params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1"}, "id_param_name": "author_id"},
    {"name": "spread_info_data_sp", "path": "/gw/api/data_sp/get_author_spread_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2"}, "id_param_name": "o_author_id"},
    {"name": "business_card_info", "path": "/gw/api/gauthor/author_get_business_card_info", "specific_params": {}, "id_param_name": "o_author_id"},
]
PROXIES = {
  "http": None,
  "https": None,
}

# --- Functions ---

def fetch_single_endpoint(session, endpoint_config, o_author_id):
    """Fetches data from a single Xingtu API endpoint. Returns endpoint name and data string (JSON or error)."""
    url = BASE_URL + endpoint_config["path"]
    query_params = endpoint_config["specific_params"].copy()
    query_params[endpoint_config["id_param_name"]] = o_author_id
    endpoint_name = endpoint_config["name"]

    data_string = None # Default to None
    print(f"  Fetching {endpoint_name}...")

    try:
        response = session.get(url, params=query_params, timeout=20)

        if response.status_code != 200:
            print(f"    HTTP Error for {endpoint_name}: Status Code {response.status_code}")
            error_message = f"HTTP Error: Status Code {response.status_code}"
            try:
                content_snippet = response.text[:500]
            except Exception:
                content_snippet = "(Could not read response text)"
            data_string = json.dumps({"error": error_message, "status": "http_error", "content_snippet": content_snippet}, ensure_ascii=False, indent=2)
            return endpoint_name, data_string

        try:
            data = response.json()
            is_success = (
                data.get("code") == 0 or
                data.get("status_code") == 0 or
                data.get("base_resp", {}).get("status_code") == 0
            )

            if is_success:
                extracted_data = data.get("data", data)
                data_string = json.dumps(extracted_data, ensure_ascii=False, indent=2)
                print(f"    Success for {endpoint_name}.")
            else:
                error_code = data.get('code', data.get('status_code', data.get('base_resp', {}).get('status_code', 'N/A')))
                error_message = data.get('message', data.get('status_message', 'N/A'))
                api_error_message = f"API Error: code={error_code}, message={error_message}"
                data_string = json.dumps({"error": api_error_message, "status": "api_error", "raw_response": data}, ensure_ascii=False, indent=2)
                print(f"    API Error for {endpoint_name}: {error_message} (Code: {error_code})")

        except json.JSONDecodeError as e:
            print(f"    Warning: JSON Decode Error for {endpoint_name}. Content: {response.text[:200]}...")
            error_message = f"JSON Decode Error: {e}"
            data_string = json.dumps({"error": error_message, "status": "json_decode_error", "content_snippet": response.text[:500]}, ensure_ascii=False, indent=2)

    except requests.exceptions.RequestException as e:
        error_message = f"Request failed: {str(e)}"
        data_string = json.dumps({"error": error_message, "status": "request_error"}, ensure_ascii=False, indent=2)
        print(f"    Request Error for {endpoint_name}: {e}")
    except Exception as e:
        error_message = f"Processing failed: {str(e)}"
        data_string = json.dumps({"error": error_message, "status": "processing_error"}, ensure_ascii=False, indent=2)
        print(f"    Unexpected Error processing {endpoint_name}: {e}")

    return endpoint_name, data_string # Return name and the JSON string (data or error)

def select_input_file():
    """Opens a dialog for the user to select the input TXT file."""
    root = tk.Tk()
    root.withdraw() # Hide the main tkinter window
    file_path = filedialog.askopenfilename(
        title="选择包含达人ID的TXT文件",
        filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
    )
    return file_path

def read_author_ids_from_file(file_path):
    """Reads author IDs from a text file, one ID per line."""
    author_ids = []
    if not file_path:
        return author_ids # Return empty list if no file was selected
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                author_id = line.strip()
                # Basic validation: check if it looks like a number and is not empty
                if author_id.isdigit():
                    author_ids.append(author_id)
                elif author_id: # Log lines that are not empty but not purely digits
                    print(f"  警告: 跳过无效行 '{author_id}' (非纯数字) in {file_path}")
        # Remove duplicates and maintain order (for Python 3.7+ dicts preserve insertion order)
        author_ids = list(dict.fromkeys(author_ids))
        print(f"从 {file_path} 读取到 {len(author_ids)} 个有效达人ID.")
    except FileNotFoundError:
        print(f"错误: 文件未找到 {file_path}")
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {e}")
    return author_ids

def write_to_excel_wide(all_authors_data, filename):
    """Writes the fetched results for multiple authors to an XLSX file."""
    if not all_authors_data:
        print("没有数据可写入文件。")
        return

    # Define header order: author_id first, then endpoints in original order
    columns_order = ['author_id'] + [ep['name'] for ep in API_ENDPOINTS]

    # Create a DataFrame from the list of dictionaries
    df = pd.DataFrame(all_authors_data)

    # Ensure author_id is the first column and reorder columns
    if 'author_id' in df.columns:
        df = df[['author_id'] + [col for col in columns_order if col in df.columns and col != 'author_id']]
    else:
        print("警告：数据中缺少 'author_id' 列。")
        df = df[columns_order[1:]] # Use endpoint names only if author_id is missing


    try:
        # Write to Excel, remove the default pandas index
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"成功将 {len(all_authors_data)} 位达人的数据写入 {filename}")
    except IOError as e:
        print(f"写入 Excel 文件 {filename} 时出错: {e}")
    except Exception as e:
        print(f"写入 Excel 时发生意外错误: {e}")

# --- Main Execution ---

if __name__ == "__main__":
    # 新增: 测试Cookie有效性
    print("正在测试Cookie有效性...")
    test_session = requests.Session()
    test_session.headers.update(HEADERS)
    test_session.proxies.update(PROXIES)
    test_session.verify = False
    
    # 选择一个简单的端点进行测试
    test_endpoint = {"name": "global_info", "path": "/gw/api/aggregator/get_author_global_info", "specific_params": {}, "id_param_name": "o_author_id"}
    test_author_id = "7289149524013121594"  # 使用一个可能存在的测试ID
    
    try:
        print(f"测试端点: {test_endpoint['name']}, 测试作者ID: {test_author_id}")
        url = BASE_URL + test_endpoint["path"]
        query_params = test_endpoint["specific_params"].copy()
        query_params[test_endpoint["id_param_name"]] = test_author_id
        
        response = test_session.get(url, params=query_params, timeout=20)
        
        if response.status_code != 200:
            print(f"警告: HTTP状态码 {response.status_code} - Cookie可能无效")
            print(f"响应内容: {response.text[:200]}...")
        else:
            data = response.json()
            if data.get("code") == 0 or data.get("status_code") == 0 or data.get("base_resp", {}).get("status_code") == 0:
                print("✓ Cookie有效! API请求成功")
            else:
                error_code = data.get('code', data.get('status_code', data.get('base_resp', {}).get('status_code', 'N/A')))
                error_message = data.get('message', data.get('status_message', 'N/A'))
                print(f"× Cookie可能无效! 错误代码: {error_code}, 错误信息: {error_message}")
                print(f"响应内容: {json.dumps(data, ensure_ascii=False)[:200]}...")
    except Exception as e:
        print(f"× 测试Cookie时发生错误: {e}")
    
    print("Cookie测试完成.\n")
    
    # 原有代码继续执行
    # Get input file from user
    input_file_path = select_input_file()
    if not input_file_path:
        print("操作已取消或未选择文件。")
        sys.exit(1)

    author_ids_to_fetch = read_author_ids_from_file(input_file_path)
    if not author_ids_to_fetch:
        print("文件中未找到有效的达人 ID 或读取文件失败。脚本终止。")
        sys.exit(1)

    # Define output filename (can be customized)
    output_filename = "authors_data_wide.xlsx"
    print(f"输出文件: {output_filename}")

    # --- Load existing data or initialize DataFrame --- 
    df_master = pd.DataFrame()
    expected_columns = ['author_id'] + [ep['name'] for ep in API_ENDPOINTS]
    if os.path.exists(output_filename):
        try:
            print(f"加载现有数据从 {output_filename}...")
            # Read author_id as string to prevent potential type issues
            df_master = pd.read_excel(output_filename, dtype={'author_id': str})
            if 'author_id' in df_master.columns:
                # --- Add duplicate handling here ---
                initial_count = len(df_master)
                df_master.drop_duplicates(subset=['author_id'], keep='first', inplace=True)
                duplicates_dropped = initial_count - len(df_master)
                if duplicates_dropped > 0:
                    print(f"  警告: 发现并移除了 {duplicates_dropped} 个基于 'author_id' 的重复行，保留了第一个出现的行.")
                # --- End duplicate handling ---

                df_master.set_index('author_id', inplace=True)
                # Ensure all expected columns exist, add if missing
                for col in expected_columns[1:]: # Skip author_id as it's the index now
                    if col not in df_master.columns:
                        df_master[col] = '' # Add missing column with empty string
                print(f"  成功加载 {len(df_master)} 条现有记录.")
            else:
                print("  警告: 现有文件中缺少 'author_id' 列. 将创建新文件.")
                df_master = pd.DataFrame(columns=expected_columns[1:]) # Create empty df with endpoint columns
                df_master.index.name = 'author_id' # Set index name

        except Exception as e:
            print(f"  警告: 无法加载或处理现有文件 {output_filename}: {e}. 将尝试创建新文件.")
            df_master = pd.DataFrame(columns=expected_columns[1:])
            df_master.index.name = 'author_id'
    else:
        print(f"文件 {output_filename} 不存在. 将创建新文件.")
        df_master = pd.DataFrame(columns=expected_columns[1:])
        df_master.index.name = 'author_id'

    # --- Initialize session --- 
    session = requests.Session()
    session.headers.update(HEADERS)
    session.proxies.update(PROXIES)
    session.verify = False

    total_authors = len(author_ids_to_fetch)
    skipped_count = 0
    fetched_count = 0
    updated_count = 0 # Count rows that were updated due to errors

    print(f"\n开始处理 {total_authors} 位达人...")

    # --- Loop through each author ID from the file --- 
    for i, author_id in enumerate(author_ids_to_fetch, 1):
        print(f"--- [{i}/{total_authors}] 处理达人 ID: {author_id} ---")
        author_id_str = str(author_id) # Ensure string comparison
        needs_fetching = True
        is_update = False

        # Check if this author_id exists in the loaded DataFrame index
        if author_id_str in df_master.index:
            print(f"  ID {author_id_str} 存在于现有数据中. 检查有效性...")
            existing_row = df_master.loc[author_id_str]
            contains_error = False
            # Check all columns (which are the endpoint names)
            for col in df_master.columns:
                cell_value = existing_row[col]
                # Check for None/NaN or the specific error string pattern
                if pd.isna(cell_value) or (isinstance(cell_value, str) and '"error":' in cell_value.lower()):
                    print(f"    发现错误或空值于列 '{col}'. 需要重新获取.")
                    contains_error = True
                    is_update = True # Mark as update because the row exists but has error
                    break

            if not contains_error:
                print("    现有数据有效. 跳过 API 获取.")
                needs_fetching = False
                skipped_count += 1
            # else: error found, needs_fetching remains True
        else:
             print(f"  ID {author_id_str} 不存在于现有数据中. 需要获取.")
             needs_fetching = True # It's a new entry
             is_update = False

        # --- Fetch from API if needed --- 
        if needs_fetching:
            if not is_update: # It's a completely new fetch
                fetched_count += 1
            else: # It's an update of an existing row with errors
                updated_count += 1
            print("  通过 API 获取数据...")
            # Use a dictionary to store fetched data for this author
            current_author_row_data = {}
            for endpoint_config in API_ENDPOINTS:
                endpoint_name, data_str = fetch_single_endpoint(session, endpoint_config, author_id)
                # Store the result (JSON string or error JSON string) in the dict
                current_author_row_data[endpoint_name] = data_str if data_str is not None else ''
                # Add sleep *after* each endpoint fetch
                print("  等待 0.1 秒...")
                time.sleep(0.1)

            # --- Update the DataFrame --- 
            # Use .loc to update the row; this adds the row if author_id_str is a new index
            try:
                df_master.loc[author_id_str] = pd.Series(current_author_row_data)
                print(f"    成功更新/添加 ID {author_id_str} 到 DataFrame.")
            except Exception as e:
                 print(f"    !!! 错误: 更新 DataFrame 时出错 for ID {author_id_str}: {e}")
                 print(f"      数据: {current_author_row_data}")
        # else: skipped fetching, row already exists and is valid

    # --- Write the updated DataFrame back to Excel --- 
    print(f"\n处理完成. 新增了 {fetched_count} 个达人数据, 更新了 {updated_count} 个错误记录, 跳过了 {skipped_count} 个有效记录.")
    if not df_master.empty:
        print(f"将 {len(df_master)} 条最终记录写入 {output_filename}...")
        try:
            # Reset index to make author_id a regular column before saving
            df_to_save = df_master.reset_index()
            # Reorder columns to ensure author_id is first, followed by endpoints
            final_columns = ['author_id'] + [ep['name'] for ep in API_ENDPOINTS]
             # Ensure all columns exist and are in the correct order
            df_to_save = df_to_save.reindex(columns=final_columns, fill_value='')

            df_to_save.to_excel(output_filename, index=False, engine='openpyxl')
            print("写入成功.")
        except Exception as e:
            print(f"写入 Excel 文件时发生错误: {e}")
    else:
        print("没有数据可写入文件 (DataFrame 为空).") 