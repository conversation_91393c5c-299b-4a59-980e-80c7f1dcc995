#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application startup test for 星图超级接口线上化
"""

import os
import sys
import time
import requests
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_basic_imports():
    """Test basic imports"""
    print("🔍 Testing basic imports...")
    
    try:
        from config.settings import settings
        print("✅ Settings imported")
        
        from core.cookie_manager import CookieManager
        print("✅ CookieManager imported")
        
        from core.xingtu_client import XingtuClient
        print("✅ XingtuClient imported")
        
        from core.excel_exporter import ExcelExporter
        print("✅ ExcelExporter imported")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_cookie_manager():
    """Test cookie manager initialization"""
    print("🔍 Testing cookie manager...")
    
    try:
        from core.cookie_manager import CookieManager
        
        # Create cookie manager (should not fail even without CookieCloud)
        cookie_manager = CookieManager()
        print("✅ CookieManager created")
        
        # Test get cookies info (should work even if no cookies)
        info = cookie_manager.get_cookies_info()
        print(f"✅ Cookie info: {info}")
        
        return True
    except Exception as e:
        print(f"❌ CookieManager error: {e}")
        return False


def test_xingtu_client():
    """Test Xingtu client initialization"""
    print("🔍 Testing Xingtu client...")
    
    try:
        from core.cookie_manager import CookieManager
        from core.xingtu_client import XingtuClient
        
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        print("✅ XingtuClient created")
        
        # Test health status
        status = xingtu_client.get_health_status()
        print(f"✅ Health status: {status}")
        
        return True
    except Exception as e:
        print(f"❌ XingtuClient error: {e}")
        return False


def test_excel_exporter():
    """Test Excel exporter initialization"""
    print("🔍 Testing Excel exporter...")
    
    try:
        from core.excel_exporter import ExcelExporter
        
        exporter = ExcelExporter()
        print("✅ ExcelExporter created")
        
        # Test export info
        info = exporter.get_export_info()
        print(f"✅ Export info: {info}")
        
        return True
    except Exception as e:
        print(f"❌ ExcelExporter error: {e}")
        return False


def test_fastapi_app():
    """Test FastAPI app creation"""
    print("🔍 Testing FastAPI app...")
    
    try:
        from main import app
        print("✅ FastAPI app created")
        
        # Test app configuration
        print(f"App title: {app.title}")
        print(f"App version: {app.version}")
        
        return True
    except Exception as e:
        print(f"❌ FastAPI app error: {e}")
        return False


def test_health_endpoint_direct():
    """Test health endpoint directly"""
    print("🔍 Testing health endpoint directly...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        response = client.get("/health")
        
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        return response.status_code in [200, 503]
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False


def test_api_endpoints():
    """Test API endpoints"""
    print("🔍 Testing API endpoints...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        print(f"Root endpoint: {response.status_code}")
        
        # Test endpoints info
        response = client.get("/endpoints")
        print(f"Endpoints info: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ API endpoints error: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Testing application startup...")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Cookie Manager", test_cookie_manager),
        ("Xingtu Client", test_xingtu_client),
        ("Excel Exporter", test_excel_exporter),
        ("FastAPI App", test_fastapi_app),
        ("Health Endpoint", test_health_endpoint_direct),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n{name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} test failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 STARTUP TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All startup tests passed!")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
