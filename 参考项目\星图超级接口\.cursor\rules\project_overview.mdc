---
description: 
globs: 
alwaysApply: false
---
# Project Overview: Xingtu Author Data Interface

This project provides tools to fetch and interact with author data from the Xingtu (星图) platform.

## Key Files:

*   **[main.py](mdc:main.py)**: Defines a FastAPI web server. It exposes an API endpoint (`/author/{o_author_id}`) that takes a Xingtu author ID and fetches comprehensive data for that author by calling multiple internal Xingtu APIs. The results are returned as a JSON object.
*   **[fetch_author_csv.py](mdc:fetch_author_csv.py)**: A command-line script that takes a Xingtu author ID, fetches data from the same internal APIs as `main.py`, and saves the raw JSON output for each API call into a single row of a CSV file. The columns correspond to the different API endpoints called.
*   **[single_author_row.csv](mdc:single_author_row.csv)**: An example output file generated by `fetch_author_csv.py`. Each cell (after the `author_id`) contains a JSON string representing the data returned by a specific Xingtu API endpoint for the author `7148640529956208654`.
*   **[requirements.txt](mdc:requirements.txt)**: Lists the Python dependencies needed for the project (`fastapi`, `uvicorn`, `requests`).

## Important Notes:

*   Both Python scripts use hardcoded `COOKIE` and `CSRF_TOKEN` values in the `HEADERS`. These are likely session-specific and may need to be updated frequently for the scripts to work.
*   The scripts disable SSL verification (`verify = False`), which is generally discouraged in production environments due to security risks.

