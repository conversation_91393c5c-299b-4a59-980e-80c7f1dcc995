#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production readiness test for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class ProductionReadinessTest:
    """Production readiness test suite"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url.rstrip("/")
        self.results = []
    
    def test_api_availability(self):
        """Test API availability"""
        print("🔍 Testing API availability...")
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            
            result = {
                "test": "api_availability",
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                data = response.json()
                result["api_name"] = data.get("name", "")
                result["api_version"] = data.get("version", "")
                print(f"✅ API available: {data.get('name')} v{data.get('version')}")
            else:
                print(f"❌ API unavailable: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ API availability error: {e}")
            self.results.append({"test": "api_availability", "success": False, "error": str(e)})
            return False
    
    def test_health_endpoint(self):
        """Test health endpoint"""
        print("🔍 Testing health endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            result = {
                "test": "health_endpoint",
                "success": response.status_code in [200, 503],  # Both healthy and unhealthy are valid
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                data = response.json()
                result["status"] = data.get("status", "unknown")
                result["components"] = list(data.get("components", {}).keys())
                
                if response.status_code == 200:
                    print("✅ Health check: Healthy")
                else:
                    print("⚠️  Health check: Unhealthy (expected without CookieCloud)")
            else:
                print(f"❌ Health endpoint error: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
            self.results.append({"test": "health_endpoint", "success": False, "error": str(e)})
            return False
    
    def test_endpoints_info(self):
        """Test endpoints information"""
        print("🔍 Testing endpoints information...")
        
        try:
            response = requests.get(f"{self.base_url}/endpoints", timeout=10)
            
            result = {
                "test": "endpoints_info",
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                data = response.json()
                result["total_endpoints"] = data.get("total_endpoints", 0)
                result["categories"] = list(data.get("categories", {}).keys())
                
                print(f"✅ Endpoints info: {result['total_endpoints']} endpoints, {len(result['categories'])} categories")
            else:
                print(f"❌ Endpoints info error: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Endpoints info error: {e}")
            self.results.append({"test": "endpoints_info", "success": False, "error": str(e)})
            return False
    
    def test_author_endpoint_validation(self):
        """Test author endpoint validation"""
        print("🔍 Testing author endpoint validation...")
        
        try:
            # Test with invalid author ID
            response = requests.get(f"{self.base_url}/author/invalid_id", timeout=10)
            
            result = {
                "test": "author_validation",
                "success": response.status_code == 400,  # Should return 400 for invalid ID
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                print("✅ Author endpoint validation working")
            else:
                print(f"❌ Author endpoint validation failed: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Author validation error: {e}")
            self.results.append({"test": "author_validation", "success": False, "error": str(e)})
            return False
    
    def test_export_endpoint_validation(self):
        """Test export endpoint validation"""
        print("🔍 Testing export endpoint validation...")
        
        try:
            # Test with invalid format
            export_data = {
                "author_id": "1234567890",
                "format": "invalid_format"
            }
            
            response = requests.post(
                f"{self.base_url}/export",
                json=export_data,
                timeout=10
            )
            
            result = {
                "test": "export_validation",
                "success": response.status_code == 400,  # Should return 400 for invalid format
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                print("✅ Export endpoint validation working")
            else:
                print(f"❌ Export endpoint validation failed: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Export validation error: {e}")
            self.results.append({"test": "export_validation", "success": False, "error": str(e)})
            return False
    
    def test_batch_endpoint_validation(self):
        """Test batch endpoint validation"""
        print("🔍 Testing batch endpoint validation...")
        
        try:
            # Test with too many author IDs
            batch_data = {
                "author_ids": ["123"] * 101,  # Exceed limit
                "format": "json"
            }
            
            response = requests.post(
                f"{self.base_url}/batch",
                json=batch_data,
                timeout=10
            )
            
            result = {
                "test": "batch_validation",
                "success": response.status_code == 400,  # Should return 400 for too many IDs
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                print("✅ Batch endpoint validation working")
            else:
                print(f"❌ Batch endpoint validation failed: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Batch validation error: {e}")
            self.results.append({"test": "batch_validation", "success": False, "error": str(e)})
            return False
    
    def test_performance(self):
        """Test basic performance"""
        print("🔍 Testing performance...")
        
        try:
            # Make multiple requests to test performance
            start_time = time.time()
            responses = []
            
            for i in range(5):
                response = requests.get(f"{self.base_url}/", timeout=10)
                responses.append(response)
            
            total_time = time.time() - start_time
            avg_time = total_time / len(responses)
            
            result = {
                "test": "performance",
                "success": all(r.status_code == 200 for r in responses),
                "total_requests": len(responses),
                "total_time": total_time,
                "avg_response_time": avg_time,
                "max_response_time": max(r.elapsed.total_seconds() for r in responses)
            }
            
            if result["success"]:
                print(f"✅ Performance test: {avg_time:.3f}s avg, {result['max_response_time']:.3f}s max")
            else:
                print("❌ Performance test failed")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Performance test error: {e}")
            self.results.append({"test": "performance", "success": False, "error": str(e)})
            return False
    
    def test_error_handling(self):
        """Test error handling"""
        print("🔍 Testing error handling...")
        
        try:
            # Test non-existent endpoint
            response = requests.get(f"{self.base_url}/nonexistent", timeout=10)
            
            result = {
                "test": "error_handling",
                "success": response.status_code == 404,  # Should return 404
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            
            if result["success"]:
                print("✅ Error handling working")
            else:
                print(f"❌ Error handling failed: {response.status_code}")
            
            self.results.append(result)
            return result["success"]
            
        except Exception as e:
            print(f"❌ Error handling test error: {e}")
            self.results.append({"test": "error_handling", "success": False, "error": str(e)})
            return False
    
    def run_all_tests(self):
        """Run all production readiness tests"""
        print("🚀 Running production readiness tests...")
        print("=" * 50)
        
        tests = [
            ("API Availability", self.test_api_availability),
            ("Health Endpoint", self.test_health_endpoint),
            ("Endpoints Info", self.test_endpoints_info),
            ("Author Validation", self.test_author_endpoint_validation),
            ("Export Validation", self.test_export_endpoint_validation),
            ("Batch Validation", self.test_batch_endpoint_validation),
            ("Performance", self.test_performance),
            ("Error Handling", self.test_error_handling)
        ]
        
        passed = 0
        total = len(tests)
        
        for name, test_func in tests:
            print(f"\n{name}:")
            print("-" * 30)
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {name} test failed: {e}")
        
        # Generate summary
        print("\n" + "=" * 50)
        print("📊 PRODUCTION READINESS SUMMARY")
        print("=" * 50)
        
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {passed / total:.2%}")
        
        # Save results
        results_file = Path("production_test_results.json")
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": time.time(),
                "summary": {
                    "total_tests": total,
                    "passed_tests": passed,
                    "success_rate": passed / total
                },
                "results": self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        if passed == total:
            print("\n🎉 All tests passed! Production deployment ready.")
            return True
        else:
            print(f"\n⚠️  {total - passed} tests failed. Review before production deployment.")
            return False


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Production readiness test for 星图超级接口线上化")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    
    args = parser.parse_args()
    
    tester = ProductionReadinessTest(args.url)
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
