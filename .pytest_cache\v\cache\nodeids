["tests/test_api.py::TestAPI::test_author_info_endpoint", "tests/test_api.py::TestAPI::test_author_info_invalid_id", "tests/test_api.py::TestAPI::test_author_info_with_category", "tests/test_api.py::TestAPI::test_author_info_with_endpoints", "tests/test_api.py::TestAPI::test_batch_endpoint_invalid_ids", "tests/test_api.py::TestAPI::test_batch_endpoint_invalid_size", "tests/test_api.py::TestAPI::test_cookie_info_endpoint", "tests/test_api.py::TestAPI::test_cookie_refresh_endpoint", "tests/test_api.py::TestAPI::test_endpoints_info", "tests/test_api.py::TestAPI::test_export_endpoint", "tests/test_api.py::TestAPI::test_export_invalid_format", "tests/test_api.py::TestAPI::test_health_endpoint", "tests/test_api.py::TestAPI::test_root_endpoint", "tests/test_api.py::TestCookieManager::test_get_cookies_info", "tests/test_api.py::TestCookieManager::test_get_xingtu_cookies", "tests/test_api.py::TestCookieManager::test_load_cookies", "tests/test_api.py::TestXingtuClient::test_get_author_info", "tests/test_api.py::TestXingtuClient::test_get_available_endpoints", "tests/test_api.py::TestXingtuClient::test_get_health_status", "tests/test_app_startup.py::test_api_endpoints", "tests/test_app_startup.py::test_basic_imports", "tests/test_app_startup.py::test_cookie_manager", "tests/test_app_startup.py::test_excel_exporter", "tests/test_app_startup.py::test_fastapi_app", "tests/test_app_startup.py::test_health_endpoint_direct", "tests/test_app_startup.py::test_xingtu_client", "tests/test_config_debug.py::test_config_structure", "tests/test_config_debug.py::test_env_file_encoding", "tests/test_config_debug.py::test_pydantic_settings", "tests/test_encoding_fix.py::test_config_import", "tests/test_encoding_fix.py::test_validation_bypass", "tests/test_integration.py::TestIntegration::TestCookieManagement::test_cookie_header_format", "tests/test_integration.py::TestIntegration::TestCookieManagement::test_cookie_refresh_cycle", "tests/test_integration.py::TestIntegration::TestCookieManagement::test_csrf_token_extraction", "tests/test_integration.py::TestIntegration::TestErrorHandling::test_export_invalid_data", "tests/test_integration.py::TestIntegration::TestErrorHandling::test_invalid_author_id", "tests/test_integration.py::TestIntegration::TestErrorHandling::test_nonexistent_category", "tests/test_integration.py::TestIntegration::TestExcelExport::test_batch_excel_export", "tests/test_integration.py::TestIntegration::TestExcelExport::test_csv_export_single_author", "tests/test_integration.py::TestIntegration::TestExcelExport::test_excel_export_single_author", "tests/test_integration.py::TestIntegration::TestRealDataFetching::test_fetch_all_endpoints", "tests/test_integration.py::TestIntegration::TestRealDataFetching::test_fetch_by_category", "tests/test_integration.py::TestIntegration::TestRealDataFetching::test_fetch_single_endpoint", "tests/test_integration.py::TestIntegration::TestRealDataFetching::test_rate_limiting", "tests/test_simple_api.py::test_api_basic", "tests/test_simple_api.py::test_with_testclient"]