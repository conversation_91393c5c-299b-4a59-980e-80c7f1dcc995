#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple API test for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def make_request(url, method="GET", data=None, timeout=10):
    """Make HTTP request using urllib"""
    try:
        if data:
            data = json.dumps(data).encode('utf-8')
        
        req = urllib.request.Request(url, data=data, method=method)
        req.add_header('Content-Type', 'application/json')
        
        start_time = time.time()
        with urllib.request.urlopen(req, timeout=timeout) as response:
            response_time = time.time() - start_time
            content = response.read().decode('utf-8')
            
            return {
                "success": True,
                "status_code": response.status,
                "content": content,
                "response_time": response_time
            }
    
    except urllib.error.HTTPError as e:
        response_time = time.time() - start_time
        content = e.read().decode('utf-8') if e.fp else ""
        
        return {
            "success": False,
            "status_code": e.code,
            "content": content,
            "response_time": response_time,
            "error": str(e)
        }
    
    except Exception as e:
        return {
            "success": False,
            "status_code": 0,
            "content": "",
            "response_time": 0,
            "error": str(e)
        }


def test_api_basic():
    """Test basic API functionality"""
    print("🚀 Testing basic API functionality...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    tests = []
    
    # Test 1: Root endpoint
    print("\n1. Testing root endpoint...")
    result = make_request(f"{base_url}/")
    if result["success"]:
        data = json.loads(result["content"])
        print(f"✅ Root endpoint: {data.get('name')} v{data.get('version')}")
        tests.append(("Root endpoint", True))
    else:
        print(f"❌ Root endpoint failed: {result.get('error', 'Unknown error')}")
        tests.append(("Root endpoint", False))
    
    # Test 2: Health endpoint
    print("\n2. Testing health endpoint...")
    result = make_request(f"{base_url}/health")
    if result["status_code"] in [200, 503]:  # Both healthy and unhealthy are valid
        data = json.loads(result["content"])
        status = data.get("status", "unknown")
        print(f"✅ Health endpoint: {status} (status code: {result['status_code']})")
        tests.append(("Health endpoint", True))
    else:
        print(f"❌ Health endpoint failed: {result.get('error', 'Unknown error')}")
        tests.append(("Health endpoint", False))
    
    # Test 3: Endpoints info
    print("\n3. Testing endpoints info...")
    result = make_request(f"{base_url}/endpoints")
    if result["success"]:
        data = json.loads(result["content"])
        total_endpoints = data.get("total_endpoints", 0)
        categories = len(data.get("categories", {}))
        print(f"✅ Endpoints info: {total_endpoints} endpoints, {categories} categories")
        tests.append(("Endpoints info", True))
    else:
        print(f"❌ Endpoints info failed: {result.get('error', 'Unknown error')}")
        tests.append(("Endpoints info", False))
    
    # Test 4: Author endpoint validation
    print("\n4. Testing author endpoint validation...")
    result = make_request(f"{base_url}/author/invalid_id")
    if result["status_code"] == 400:
        print("✅ Author validation: Correctly rejects invalid ID")
        tests.append(("Author validation", True))
    else:
        print(f"❌ Author validation failed: Expected 400, got {result['status_code']}")
        tests.append(("Author validation", False))
    
    # Test 5: Export endpoint validation
    print("\n5. Testing export endpoint validation...")
    export_data = {
        "author_id": "1234567890",
        "format": "invalid_format"
    }
    result = make_request(f"{base_url}/export", method="POST", data=export_data)
    if result["status_code"] == 400:
        print("✅ Export validation: Correctly rejects invalid format")
        tests.append(("Export validation", True))
    else:
        print(f"❌ Export validation failed: Expected 400, got {result['status_code']}")
        tests.append(("Export validation", False))
    
    # Test 6: Performance test
    print("\n6. Testing performance...")
    start_time = time.time()
    response_times = []
    
    for i in range(3):
        result = make_request(f"{base_url}/")
        if result["success"]:
            response_times.append(result["response_time"])
    
    total_time = time.time() - start_time
    
    if len(response_times) == 3:
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        print(f"✅ Performance: {avg_time:.3f}s avg, {max_time:.3f}s max")
        tests.append(("Performance", True))
    else:
        print("❌ Performance test failed")
        tests.append(("Performance", False))
    
    # Test 7: Error handling
    print("\n7. Testing error handling...")
    result = make_request(f"{base_url}/nonexistent")
    if result["status_code"] == 404:
        print("✅ Error handling: Correctly returns 404 for non-existent endpoint")
        tests.append(("Error handling", True))
    else:
        print(f"❌ Error handling failed: Expected 404, got {result['status_code']}")
        tests.append(("Error handling", False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("\n🎉 All tests passed! API is production ready.")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        return False


def test_with_testclient():
    """Test using FastAPI TestClient"""
    print("\n🧪 Testing with FastAPI TestClient...")
    
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        print(f"Root endpoint: {response.status_code}")
        
        # Test health endpoint
        response = client.get("/health")
        print(f"Health endpoint: {response.status_code}")
        
        # Test endpoints info
        response = client.get("/endpoints")
        print(f"Endpoints info: {response.status_code}")
        
        print("✅ TestClient tests completed")
        return True
        
    except Exception as e:
        print(f"❌ TestClient error: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Simple API Test for 星图超级接口线上化")
    print("=" * 60)
    
    # Test basic API functionality
    api_success = test_api_basic()
    
    # Test with TestClient
    testclient_success = test_with_testclient()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL SUMMARY")
    print("=" * 60)
    
    if api_success and testclient_success:
        print("🎉 All tests passed! The API is working correctly and ready for production.")
        print("\n✅ Key features verified:")
        print("  - API endpoints responding correctly")
        print("  - Health checks working")
        print("  - Input validation functioning")
        print("  - Error handling proper")
        print("  - Performance acceptable")
        print("\n🚀 Ready for deployment!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
