import pandas as pd
import numpy as np
import json
import re
from sklearn.feature_selection import Varian<PERSON><PERSON>hreshold
from sentence_transformers import SentenceTransformer
import warnings
import argparse

# --- Configuration ---
# Suppress warnings for cleaner output (optional)
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# Threshold for dropping columns with too many missing values
MISSING_THRESHOLD = 0.8

# Threshold for variance thresholding (adjust as needed)
VARIANCE_THRESHOLD = 0.01 # Drops columns where most values are the same

# --- Embedding Configuration ---
# EMBEDDING_MODEL_NAME = 'paraphrase-multilingual-MiniLM-L12-v2' # Example multilingual
# EMBEDDING_MODEL_NAME = None # Set to None to disable embedding
EMBEDDING_MODEL_NAME = 'shibing624/text2vec-base-chinese' # Use specified Chinese model
HIGH_CARD_THRESHOLD = 30      # Switch from Multi-Hot to Embedding above this many unique values in a list
CACHE_EMB = {}                # Cache for sentence embeddings
EMBEDDING_DIM = 0             # Will be set after loading the model
EMPTY_EMBEDDING = np.array([]) # Will be set after loading the model

# List of columns containing text to be embedded (adjust based on actual flattened columns)
# Example: ['hot_comment_tokens_flat', 'mcn_introduction']
# This is for *single* text fields, list fields are handled in post-scan now
TEXT_COLUMNS_TO_EMBED = []

# List of columns to be dropped explicitly (metadata, IDs, URLs etc.)
# Example: ['base_resp_status_code', 'author_avatar_uri', 'qr_code_url']
COLUMNS_TO_DROP_EXPLICITLY = []

# List of columns containing nested lists for multi-hot encoding
# Example: {'industry_list_flat': 'industry', 'aweme_tags_flat': 'tag'} # map: {column_name: prefix_for_new_columns}
LIST_COLUMNS_TO_MULTI_HOT = {}


# --- Helper Functions ---

def safe_json_parse(json_string):
    """Safely parses a JSON string, returning None on failure or if input is not a string."""
    if not isinstance(json_string, str):
        return None # Handle NaN or non-string inputs
    try:
        # Basic check for our known error structure
        if json_string.strip().startswith('{"error":'):
             # Could extract error type here if needed, for now just return None
             return None
        return json.loads(json_string)
    except json.JSONDecodeError:
        return None # Return None if parsing fails

def flatten_json_recursively(data, parent_key='', sep='_'):
    """Recursively flattens a nested dictionary or list."""
    items = {}
    if isinstance(data, dict):
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            items.update(flatten_json_recursively(v, new_key, sep=sep))
    elif isinstance(data, list):
        # Option 1: Ignore lists
        # items[parent_key] = data # Keep list as is (problematic for DataFrame)
        # Option 2: Join list items (if strings)
        # items[parent_key] = ', '.join(map(str, data))
        # Option 3: Expand list into numbered keys (can create many columns)
        for i, v in enumerate(data):
             new_key = f"{parent_key}{sep}{i}" if parent_key else str(i)
             items.update(flatten_json_recursively(v, new_key, sep=sep))
        # Option 4: Mark as list for later processing (e.g., multi-hot) - Requires modification
        # items[parent_key] = data # Requires downstream handler

    else:
        items[parent_key] = data
    return items

def parse_range_string(range_str):
    """Parses strings like '5k-10k', '10w+', '>500' into min, max, mid. Handles N/A etc."""
    if pd.isna(range_str) or not isinstance(range_str, str):
        return np.nan, np.nan, np.nan

    range_str = range_str.lower().strip()
    min_val, max_val, mid_val = np.nan, np.nan, np.nan

    def convert_unit(value_str):
        value_str = value_str.strip()
        num_part = re.match(r"([\d.]+)", value_str)
        if not num_part: return np.nan
        num = float(num_part.group(1))
        if 'k' in value_str: num *= 1000
        if 'w' in value_str: num *= 10000
        return num

    # Pattern: X-Y (e.g., 5k-10k)
    match = re.match(r"([\d.]+[kw]?)\s*-\s*([\d.]+[kw]?)", range_str)
    if match:
        min_val = convert_unit(match.group(1))
        max_val = convert_unit(match.group(2))

    # Pattern: X+ (e.g., 10w+)
    match = re.match(r"([\d.]+[kw]?)\+", range_str)
    if match:
        min_val = convert_unit(match.group(1))
        # max_val remains NaN

    # Pattern: <X or ≤X (e.g., <500)
    match = re.match(r"[<≤]\s*([\d.]+[kw]?)", range_str)
    if match:
        max_val = convert_unit(match.group(1))
        # min_val remains NaN (or could be set to 0?)

    # Pattern: >X or ≥X (e.g., >5k)
    match = re.match(r"[>≥]\s*([\d.]+[kw]?)", range_str)
    if match:
        min_val = convert_unit(match.group(1))
        # max_val remains NaN

    # Pattern: Single number (e.g., 5000)
    match = re.match(r"^([\d.]+[kw]?)$", range_str)
    if match:
        min_val = max_val = convert_unit(match.group(1))

    # Calculate midpoint if bounds exist
    if not pd.isna(min_val) and not pd.isna(max_val):
        mid_val = (min_val + max_val) / 2
    elif not pd.isna(min_val):
        mid_val = min_val # Use min if max is unknown
    elif not pd.isna(max_val):
        mid_val = max_val # Use max if min is unknown

    return min_val, max_val, mid_val

# --- Helper Functions (追加) ---
def summarize_list_of_dict(lst, top_k=3, cnt_key='cnt', name_key='name', prop_key='proportion'):
    """
    将 [{'cnt':5,'name':'亲子',...}, …] → {
        'cnt_sum': 12,
        'tag_1': '亲子', 'tag_1_prop': 0.83,
        ...
    }
    """
    if not isinstance(lst, list) or len(lst) == 0:
        return {}

    # Try to intelligently guess keys if standard ones aren't present
    if lst and not any(cnt_key in d for d in lst):
        potential_cnt_keys = ['count', 'value', 'num', 'freq']
        for key in potential_cnt_keys:
            if key in lst[0]:
                cnt_key = key
                # print(f"  Auto-detected cnt_key: {cnt_key}") # Optional debug
                break
    if lst and not any(name_key in d for d in lst):
        potential_name_keys = ['key', 'label', 'tag', 'item']
        for key in potential_name_keys:
            if key in lst[0]:
                name_key = key
                # print(f"  Auto-detected name_key: {name_key}") # Optional debug
                break
    if lst and not any(prop_key in d for d in lst):
        potential_prop_keys = ['ratio', 'percent', 'score']
        for key in potential_prop_keys:
            if key in lst[0]:
                prop_key = key
                # print(f"  Auto-detected prop_key: {prop_key}") # Optional debug
                break

    # 按 cnt 排序取前 k
    try:
        # Handle potential non-numeric cnt values gracefully
        lst_sorted = sorted(
            [d for d in lst if isinstance(d.get(cnt_key), (int, float))],
            key=lambda d: d.get(cnt_key, 0),
            reverse=True
        )[:top_k]
        # Calculate sum only on numeric counts
        cnt_sum_val = sum(d.get(cnt_key, 0) for d in lst if isinstance(d.get(cnt_key), (int, float)))
    except Exception as e:
        # print(f"  Error sorting/summing list for summary: {e}") # Optional debug
        return {}

    out = {f'{cnt_key}_sum': cnt_sum_val}
    for i, d in enumerate(lst_sorted, 1):
        # Use a generic prefix like 'item' instead of 'tag' for broader applicability
        out[f'item_{i}_name'] = d.get(name_key)
        # Only add prop if the key exists in the dict
        if prop_key in d:
            out[f'item_{i}_prop'] = d.get(prop_key)
        # Optionally add the count for the item itself
        out[f'item_{i}_cnt'] = d.get(cnt_key)

    return out

def check_series_type(series):
    """Checks the dominant non-null type and consistency."""
    # Drop NaNs first to analyze actual content
    series_dropped_na = series.dropna()
    if series_dropped_na.empty:
        return 'empty_or_na' # Column has no valid data to check

    # Use the type of the first valid item as a reference
    first_valid_item = series_dropped_na.iloc[0]

    if isinstance(first_valid_item, list):
        # Check if it's consistently list-of-dict
        is_list_of_dict_consistent = all(isinstance(x, list) and x and isinstance(x[0], dict) for x in series_dropped_na)
        if is_list_of_dict_consistent:
            return 'list_of_dict'

        # Check if it's consistently list-of-primitive (includes empty lists)
        is_list_of_primitive_consistent = all(isinstance(x, list) and (not x or not isinstance(x[0], dict)) for x in series_dropped_na)
        if is_list_of_primitive_consistent:
            return 'list_of_primitive'

        # Check if it's consistently empty lists
        is_list_empty_consistent = all(isinstance(x, list) and not x for x in series_dropped_na)
        if is_list_empty_consistent:
            return 'list_empty'

        return 'list_mixed' # Contains lists, but inconsistent internal types or structure

    elif isinstance(first_valid_item, dict):
        # Check if consistently dict
        is_dict_consistent = all(isinstance(x, dict) for x in series_dropped_na)
        if is_dict_consistent:
            return 'dict'
        else:
            return 'mixed' # Contains dicts and other types
    else:
        # Assume primitive or other simple types if not list or dict
        # Could add more checks here if needed (e.g., for strings that look like JSON)
        return 'primitive_or_other'

# --- Helper Functions (Embedding) ---
def encode_text(text, model):
    """Encodes text using the provided model, with caching."""
    # Ensure text is a string, handle potential NaN or other types
    text = str(text) if pd.notna(text) else ''
    if not text: # Handle empty strings after potential NaN conversion
        return EMPTY_EMBEDDING

    if text in CACHE_EMB:
        return CACHE_EMB[text]

    try:
        # show_progress_bar=False to avoid flooding console when called repeatedly
        vec = model.encode(text, show_progress_bar=False)
        CACHE_EMB[text] = vec
        return vec
    except Exception as e:
        # print(f"Warning: Failed to encode text '{text[:50]}...': {e}")
        return EMPTY_EMBEDDING # Return empty embedding on error


# --- Main Processing Function ---

def process_data(input_file, output_file):
    """Loads, flattens, cleans, and processes the author data."""
    print(f"Loading data from {input_file}...")
    try:
        df_wide = pd.read_excel(input_file)
    except FileNotFoundError:
        print(f"Error: Input file not found at {input_file}")
        return

    if 'author_id' not in df_wide.columns:
        print("Error: 'author_id' column not found in the input file.")
        return

    print("Starting JSON parsing and flattening...")
    all_flattened_data = []
    # Identify columns containing JSON data (heuristic: check if values are strings starting with { or [)
    json_like_columns = [
        col for col in df_wide.columns
        if col != 'author_id' and df_wide[col].apply(lambda x: isinstance(x, str) and (x.strip().startswith('{') or x.strip().startswith('['))).any()
    ]
    print(f"Identified {len(json_like_columns)} potential JSON columns: {json_like_columns}")

    # Process each row
    for index, row in df_wide.iterrows():
        print(f"  Processing row {index+1}/{len(df_wide)} (Author ID: {row['author_id']})")
        flat_row_data = {'author_id': row['author_id']}
        for col in json_like_columns:
            parsed_data = safe_json_parse(row[col])
            if parsed_data:
                # ---------- NEW: 自动处理 list-of-dict ----------
                if isinstance(parsed_data, list) and parsed_data and all(isinstance(x, dict) for x in parsed_data):
                    # Use default keys initially, the function will try to auto-detect better ones
                    summary_dict = summarize_list_of_dict(parsed_data, top_k=3)
                    # Prefix the keys from the summary
                    prefixed_summary = {f"{col}_{k}": v for k, v in summary_dict.items()}
                    flat_row_data.update(prefixed_summary)
                    # print(f"    Summarized list-of-dict for column: {col}") # Optional debug print
                    continue  # Skip subsequent normalize/flatten for this column
                # ---------- 旧逻辑 ----------
                try:
                    # Attempt normalization - might fail on simple values or lists
                    normalized = pd.json_normalize(parsed_data, sep='_')
                    # Prefix column names
                    normalized.columns = [f"{col}_{c}" for c in normalized.columns]
                    # Merge into the flat_row_data - assumes json_normalize returns one row
                    if not normalized.empty: # Check if normalization produced columns
                        flat_row_data.update(normalized.iloc[0].to_dict())
                except Exception as e:
                    # Fallback for simple values or structures json_normalize struggles with
                    # print(f"    json_normalize failed for {col}, using recursive flatten (Error: {e})")
                    flattened = flatten_json_recursively(parsed_data, parent_key=col, sep='_')
                    flat_row_data.update(flattened)
            # else: Error or non-JSON, already handled by safe_json_parse

        # Add non-JSON columns from the original row as well
        for col in df_wide.columns:
            if col not in json_like_columns and col != 'author_id':
                 # Prefix non-JSON columns too for consistency? Maybe not needed.
                 flat_row_data[col] = row[col]

        all_flattened_data.append(flat_row_data)

    # Create DataFrame from flattened data
    df_flat = pd.DataFrame(all_flattened_data)
    print(f"Initial flattened table shape: {df_flat.shape}")

    # --- Embedding Model Loading ---
    model = None
    # Allow modification of globals, including potentially disabling embedding on error
    global EMBEDDING_DIM, EMPTY_EMBEDDING, EMBEDDING_MODEL_NAME
    if EMBEDDING_MODEL_NAME:
        print(f"Loading embedding model: {EMBEDDING_MODEL_NAME}...")
        try:
            model = SentenceTransformer(EMBEDDING_MODEL_NAME)
            EMBEDDING_DIM = model.get_sentence_embedding_dimension()
            EMPTY_EMBEDDING = np.zeros(EMBEDDING_DIM) # Define empty embedding based on model
            print(f"Model loaded. Embedding dimension: {EMBEDDING_DIM}")
        except Exception as e:
            print(f"Error loading embedding model '{EMBEDDING_MODEL_NAME}': {e}")
            print("*** Embedding will be disabled for this run. ***")
            model = None # Ensure model is None if loading failed
            EMBEDDING_MODEL_NAME = None # Disable embedding if model failed to load

    # --- Post-Flattening Scan for Residual Objects ---
    print("Post-flatten scan for residual list/dict columns...")
    extra_columns_dict = {} # Use dict to store new DFs
    cols_to_drop_after_scan = [] # Keep track of columns processed here

    # Identify columns that still contain complex objects (like lists or dicts)
    # A simple check is to see if the dtype is 'object' and it's not just strings/Nones
    potential_object_cols = df_flat.select_dtypes(include=['object']).columns.tolist()
    if 'author_id' in potential_object_cols: potential_object_cols.remove('author_id') # Don't process author_id

    for col in potential_object_cols:
        if col not in df_flat.columns: continue # Skip if already dropped

        series = df_flat[col]
        # Determine the consistent type within the column (ignoring NaNs)
        series_type = check_series_type(series)

        try:
            if series_type == 'list_of_dict':
                # Determine keys for weighted embedding (similar logic to summarize_list_of_dict)
                temp_list = series.dropna().iloc[0] if not series.dropna().empty else []
                cnt_key = 'cnt'
                name_key = 'name'
                if temp_list:
                     potential_cnt_keys = ['count', 'value', 'num', 'freq']
                     if not any(cnt_key in d for d in temp_list):
                         for key in potential_cnt_keys:
                             if key in temp_list[0]: cnt_key = key; break
                     potential_name_keys = ['key', 'label', 'tag', 'item']
                     if not any(name_key in d for d in temp_list):
                          for key in potential_name_keys:
                             if key in temp_list[0]: name_key = key; break

                # Weighted embedding logic (replaces summarization for these columns)
                if model:
                    print(f"  [post-scan] weighted embedding list-of-dict: '{col}' (using name='{name_key}', weight='{cnt_key}')")
                    def weighted_embed(lst):
                        if not isinstance(lst, list) or not lst:
                            return EMPTY_EMBEDDING
                        # Ensure weights are numeric, default to 1 if missing/invalid
                        weights = np.array([d.get(cnt_key, 1) if isinstance(d.get(cnt_key), (int, float)) else 1 for d in lst])
                        total_weight = np.sum(weights)
                        if total_weight == 0: # Avoid division by zero if all weights are 0
                            return EMPTY_EMBEDDING
                        # Encode names and apply weights
                        weighted_vecs = [
                            encode_text(d.get(name_key, ''), model) * (weights[i] / total_weight)
                            for i, d in enumerate(lst)
                        ]
                        # Filter out empty embeddings before summing
                        valid_vecs = [v for v in weighted_vecs if v.size > 0]
                        if not valid_vecs:
                            return EMPTY_EMBEDDING
                        return np.sum(valid_vecs, axis=0)

                    emb_matrix = np.vstack(series.apply(weighted_embed).values)
                    emb_cols = [f"{col}_w_emb_{i}" for i in range(EMBEDDING_DIM)]
                    new_df = pd.DataFrame(emb_matrix, columns=emb_cols, index=series.index)
                    extra_columns_dict[col] = new_df
                    cols_to_drop_after_scan.append(col)
                else:
                    print(f"  [post-scan] Skipping embedding for list-of-dict '{col}' (no model loaded)")

            elif series_type == 'list_of_primitive':
                valid_lists = series.dropna().apply(lambda x: x if isinstance(x, list) else [])
                if valid_lists.empty or not any(valid_lists):
                    # print(f"    Skipping empty/NaN list column '{col}'") # Less verbose
                    continue

                flat_list = [item for sublist in valid_lists for item in sublist]
                uniq_vals = pd.Series(flat_list).unique()

                if model and len(uniq_vals) >= HIGH_CARD_THRESHOLD:
                    print(f"  [post-scan] embedding list-of-primitive: '{col}' ({len(uniq_vals)} unique values >= {HIGH_CARD_THRESHOLD})")
                    # Mean embedding aggregation
                    def agg_embed(lst):
                        if not isinstance(lst, list) or not lst:
                            return EMPTY_EMBEDDING
                        # Encode items
                        vecs = [encode_text(item, model) for item in lst]
                        # Filter out empty embeddings before averaging
                        valid_vecs = [v for v in vecs if v.size > 0]
                        if not valid_vecs:
                             return EMPTY_EMBEDDING
                        return np.mean(valid_vecs, axis=0)

                    emb_matrix = np.vstack(series.apply(agg_embed).values)
                    emb_cols = [f"{col}_emb_{i}" for i in range(EMBEDDING_DIM)]
                    new_df = pd.DataFrame(emb_matrix, columns=emb_cols, index=series.index)
                    extra_columns_dict[col] = new_df
                    cols_to_drop_after_scan.append(col)
                elif 2 <= len(uniq_vals) < HIGH_CARD_THRESHOLD:
                    # Fallback to Multi-Hot for low/medium cardinality
                    print(f"  [post-scan] multi-hot encoding list-of-primitive: '{col}' ({len(uniq_vals)} unique values < {HIGH_CARD_THRESHOLD})")
                    try:
                        from sklearn.preprocessing import MultiLabelBinarizer
                        mlb = MultiLabelBinarizer()
                        # Ensure all items are strings for MLB
                        str_lists = valid_lists.apply(lambda l: [str(item) for item in l])
                        mlb_df = pd.DataFrame(mlb.fit_transform(str_lists), columns=mlb.classes_, index=valid_lists.index)
                        mlb_df = mlb_df.add_prefix(f"{col}_")
                        extra_columns_dict[col] = mlb_df # Store for later merge
                        cols_to_drop_after_scan.append(col)
                    except Exception as mlb_error:
                         print(f"    !! MultiLabelBinarizer error on '{col}': {mlb_error}")
                else:
                    # Case: Only 1 unique value, or threshold is < 2, or model is None and cardinality is high
                    print(f"    Skipping multi-hot/embedding for '{col}' (unique count {len(uniq_vals)} not in range or embedding disabled)")

            elif series_type == 'dict':
                print(f"  [post-scan] re-flattening dict: '{col}'")
                # Apply flatten_json_recursively safely to each dict in the series
                reflatted_series = series.apply(lambda d: flatten_json_recursively(d, parent_key=col) if isinstance(d, dict) else {})
                # Convert the series of dicts into a DataFrame
                new_df = pd.DataFrame(reflatted_series.tolist(), index=series.index)
                # No need to add prefix here as flatten_json_recursively should handle it
                extra_columns_dict[col] = new_df
                cols_to_drop_after_scan.append(col)

            # elif series_type == 'list_empty':
            #     print(f"  [post-scan] Note: Column '{col}' contains only empty lists or NaNs.")
            #     # Decide whether to drop these later or keep them
            #     # cols_to_drop_after_scan.append(col)

            # elif series_type in ['mixed', 'list_mixed']:
            #     print(f"  [post-scan] Warning: Column '{col}' has mixed types, skipping further flattening.")

        except Exception as e:
            print(f"    !! Error during post-scan processing of '{col}' ({series_type}): {e}")

    # Merge new features and drop original processed columns
    if extra_columns_dict:
        print(f"Merging {len(extra_columns_dict)} sets of post-processed features...")
        # Start with the original df_flat
        processed_df_list = [df_flat]
        # Add the new dataframes generated from the processed columns
        processed_df_list.extend(extra_columns_dict.values())

        # Use concat (aligns index automatically)
        df_flat = pd.concat(processed_df_list, axis=1)

        # Drop the original columns that were successfully processed in this scan
        # Use list(set(...)) to handle potential duplicates if a column was somehow processed twice
        df_flat = df_flat.drop(columns=list(set(cols_to_drop_after_scan)), errors='ignore')

    print(f"After post-scan flatten shape: {df_flat.shape}")

    # --- Basic Cleaning ---
    print("Applying basic cleaning...")
    # Drop explicitly defined useless columns (run this again in case new useless columns were generated)
    # Also, ensure the columns targeted for dropping actually exist
    current_cols_to_drop = [c for c in COLUMNS_TO_DROP_EXPLICITLY if c in df_flat.columns]
    if current_cols_to_drop:
        df_clean = df_flat.drop(columns=current_cols_to_drop)
        print(f"  Dropped explicitly defined columns: {current_cols_to_drop}")
    else:
        df_clean = df_flat # No columns to drop
        print("  No explicitly defined columns to drop were found.")
    print(f"  Shape after explicit drop: {df_clean.shape}")

    # --- Feature Engineering & Type Conversion ---
    print("Applying feature engineering and type conversion...")

    # Example: Apply range parsing to a hypothetical column
    # if 'commerce_seed_base_info_avg_sales_amount_range' in df_clean.columns:
    #     print("  Parsing range string column...")
    #     range_parsed = df_clean['commerce_seed_base_info_avg_sales_amount_range'].apply(parse_range_string)
    #     df_clean[['sales_min', 'sales_max', 'sales_mid']] = pd.DataFrame(range_parsed.tolist(), index=df_clean.index)
    #     # Optionally drop the original range string column
    #     # df_clean = df_clean.drop(columns=['commerce_seed_base_info_avg_sales_amount_range'])

    # Example: Apply multi-hot encoding
    print("  Applying multi-hot encoding...")
    for list_col, prefix in LIST_COLUMNS_TO_MULTI_HOT.items():
        if list_col in df_clean.columns:
            print(f"    Processing multi-hot for: {list_col} with prefix '{prefix}_'")
            # Ensure the column contains lists (might need parsing if stored as stringified list)
            # Placeholder: assumes column contains actual lists or NaN
            try:
                 df_clean[list_col] = df_clean[list_col].apply(lambda x: x if isinstance(x, list) else []) # Ensure list type
                 mlb_df = df_clean[list_col].apply(pd.Series).stack().str.get_dummies().sum(level=0)
                 mlb_df.columns = [f"{prefix}_{col}" for col in mlb_df.columns]
                 df_clean = pd.concat([df_clean, mlb_df], axis=1)
                 # df_clean = df_clean.drop(columns=[list_col]) # Optionally drop original
            except Exception as e:
                 print(f"    Could not multi-hot encode {list_col}: {e}")


    # Attempt numeric conversion for all possible columns
    print("  Attempting conversion to numeric...")
    for col in df_clean.columns:
        if col != 'author_id': # Keep author_id as object/string
             df_clean[col] = pd.to_numeric(df_clean[col], errors='ignore') # Ignore columns that can't be converted

    # --- Text Embedding ---
    if EMBEDDING_MODEL_NAME and TEXT_COLUMNS_TO_EMBED:
        print(f"Applying text embeddings using '{EMBEDDING_MODEL_NAME}'...")
        try:
            model = SentenceTransformer(EMBEDDING_MODEL_NAME)
            for col in TEXT_COLUMNS_TO_EMBED:
                if col in df_clean.columns:
                    print(f"  Embedding column: {col}")
                    # Fill NaNs with empty string for embedding
                    texts = df_clean[col].fillna('').tolist()
                    embeddings = model.encode(texts, show_progress_bar=True)
                    # Add embeddings as new columns
                    embed_cols = [f"{col}_embed_{i}" for i in range(embeddings.shape[1])]
                    df_embed = pd.DataFrame(embeddings, index=df_clean.index, columns=embed_cols)
                    df_clean = pd.concat([df_clean, df_embed], axis=1)
                    # Optionally drop the original text column
                    # df_clean = df_clean.drop(columns=[col])
                else:
                    print(f"  Warning: Text column '{col}' not found for embedding.")
        except Exception as e:
            print(f"  Error during text embedding: {e}. Skipping embedding.")
    else:
        print("Skipping text embedding (no model name or columns specified).")


    # --- Filtering Stage ---
    print("Applying filtering...")
    # 1. Drop columns with high missing rate
    missing_rates = df_clean.isnull().mean()
    cols_to_drop_missing = missing_rates[missing_rates > MISSING_THRESHOLD].index.tolist()
    # Avoid dropping author_id if it somehow has missing values (shouldn't happen)
    if 'author_id' in cols_to_drop_missing: cols_to_drop_missing.remove('author_id')
    df_filtered = df_clean.drop(columns=cols_to_drop_missing)
    print(f"  Dropped {len(cols_to_drop_missing)} columns due to >{MISSING_THRESHOLD*100}% missing values.")
    print(f"  Shape after missing value filtering: {df_filtered.shape}")

    # 2. Drop low variance columns (constants or near-constants)
    # Select only numeric columns for variance thresholding
    numeric_cols = df_filtered.select_dtypes(include=np.number).columns.tolist()
    # Remove author_id if it was numeric
    if 'author_id' in numeric_cols: numeric_cols.remove('author_id')

    if numeric_cols:
        selector = VarianceThreshold(threshold=VARIANCE_THRESHOLD)
        try:
            # Fit requires non-NaN values. Fill NaNs temporarily (e.g., with 0 or mean)
            # A better approach might be imputation before variance check.
            selector.fit(df_filtered[numeric_cols].fillna(0))
            low_variance_cols = df_filtered[numeric_cols].columns[~selector.get_support()].tolist()
            df_filtered = df_filtered.drop(columns=low_variance_cols)
            print(f"  Dropped {len(low_variance_cols)} low variance numeric columns (threshold={VARIANCE_THRESHOLD}).")
            # print(f"    Low variance columns dropped: {low_variance_cols}") # Optional: print names
        except Exception as e:
            print(f"  Could not apply variance thresholding: {e}")
    else:
        print("  No numeric columns found for variance thresholding.")

    print(f"  Final processed shape: {df_filtered.shape}")

    # --- Save Output ---
    print(f"Saving processed data to {output_file}...")
    try:
        # Choose output format based on extension
        if output_file.endswith('.csv'):
            df_filtered.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif output_file.endswith('.xlsx'):
            df_filtered.to_excel(output_file, index=False, engine='openpyxl')
        else:
            print("Warning: Output file extension not recognized (.csv or .xlsx). Saving as CSV.")
            df_filtered.to_csv(output_file, index=False, encoding='utf-8-sig')
        print("Processing complete.")
    except Exception as e:
        print(f"Error saving output file: {e}")


# --- Command Line Interface ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Flatten, clean, and process Xingtu author data from wide Excel format.")
    parser.add_argument("input_file", help="Path to the input wide-format Excel file (e.g., author_data_wide.xlsx).")
    parser.add_argument("-o", "--output", default="processed_features.csv", help="Path to save the processed output file (CSV or XLSX).")
    # Future arguments could control thresholds, models, specific columns, etc.

    args = parser.parse_args()
    process_data(args.input_file, args.output) 