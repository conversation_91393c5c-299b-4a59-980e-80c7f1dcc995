
#!/usr/bin/env python
"""flatten_json_csv_v2.py

改进点：
1. 对 audience_distribution.distributions / 任意 *distribution_list*
   **按 type_display 合并**，不携带 [index]，避免列名错位：
      观众分布.城市等级分布.四线  (所有行一致)

2. 对其他 kv 列表 (category_items 等) 同理，用 name 合并。

用法：
    python flatten_json_csv_v2.py input.csv output.csv
"""

import sys, json, pandas as pd, re

def safe_load(x):
    try:
        return json.loads(x) if pd.notnull(x) else {}
    except Exception:
        return {}

# ---------- 核心递归 ----------
def flatten(obj, path, out):
    if isinstance(obj, dict):
        # 优先处理含 distribution_list
        if 'distribution_list' in obj and isinstance(obj['distribution_list'], list):
            tdisp = obj.get('type_display') or str(obj.get('type', ''))
            for dist in obj['distribution_list']:
                if isinstance(dist, dict) and 'distribution_key' in dist:
                    key = dist['distribution_key']
                    val = dist['distribution_value']
                    col = f'{path}.{tdisp}.{key}' if path else f'{tdisp}.{key}'
                    out[col] = val
        # 含 category_items
        if 'category_items' in obj and isinstance(obj['category_items'], list):
            for item in obj['category_items']:
                if isinstance(item, dict) and 'name' in item:
                    name = item['name']
                    val  = item.get('rate') or item.get('sale_amount_range') or item.get('value')
                    col  = f'{path}.类目.{name}' if path else f'类目.{name}'
                    out[col] = val
        # 递归其余键
        for k, v in obj.items():
            if k in ('distribution_list', 'category_items'):
                continue
            new_path = f'{path}.{k}' if path else k
            flatten(v, new_path, out)
    elif isinstance(obj, list):
        # 如果父路径以 ".distributions" 结尾，则跳过索引（已由 type_display 区分）
        if path.endswith('.distributions'):
            for item in obj:
                flatten(item, path, out)  # 不加索引
        else:
            for idx, item in enumerate(obj):
                flatten(item, f'{path}[{idx}]', out)
    else:
        out[path] = obj

def flatten_series(series: pd.Series, prefix: str) -> pd.DataFrame:
    rows = []
    for txt in series:
        node = safe_load(txt)
        d = {}
        flatten(node, prefix, d)
        rows.append(d)
    return pd.DataFrame(rows)

def main(inp, outp):
    df = pd.read_csv(inp)
    json_cols = [c for c in df.columns if df[c].astype(str).str.startswith('{').all()]
    parts = [df[[c for c in df.columns if c not in json_cols]]]  # keep non-JSON
    for col in json_cols:
        parts.append(flatten_series(df[col], col))
    df_flat = pd.concat(parts, axis=1)

    # 可选：排序列，易读
    df_flat = df_flat.reindex(sorted(df_flat.columns), axis=1)

    df_flat.to_csv(outp, index=False, encoding='utf-8-sig')
    print(f'>> Flattened file saved: {outp}  (shape: {df_flat.shape})')

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print('Usage: python flatten_json_csv_v2.py input.csv output.csv')
        sys.exit(1)
    main(sys.argv[1], sys.argv[2])
