# 星图超级接口线上化 (Xingtu Super API Online)

A production-ready FastAPI application for fetching comprehensive influencer data from the Xingtu (星图) platform with dynamic cookie management and Excel export capabilities.

## 🚀 Features

- **Dynamic Cookie Management**: Secure authentication using CookieCloud integration
- **Comprehensive Data Fetching**: Access to 25+ Xingtu API endpoints
- **Multiple Export Formats**: Excel, CSV, and JSON export capabilities
- **Production Ready**: Docker deployment with health checks and monitoring
- **Rate Limiting**: Built-in request throttling and retry mechanisms
- **Robust Error Handling**: Comprehensive logging and error recovery
- **Batch Processing**: Support for multiple author data fetching
- **RESTful API**: Clean, documented API endpoints

## 📋 Prerequisites

- Python 3.11+
- Docker and Docker Compose (for containerized deployment)
- CookieCloud server with Xingtu cookies
- Valid Xingtu account cookies

## 🛠️ Installation

### Local Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd 星图超级接口线上化
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run the application**
```bash
python main.py
```

### Docker Deployment

1. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your production configuration
```

2. **Build and run with Docker Compose**
```bash
# Basic deployment
docker-compose up -d

# With Nginx reverse proxy
docker-compose --profile production up -d

# With Redis caching
docker-compose --profile cache up -d
```

## ⚙️ Configuration

### Required Environment Variables

```bash
# CookieCloud Configuration (Required)
COOKIECLOUD_SERVER_URL=http://your-cookiecloud-server:8088
COOKIECLOUD_UUID=your-uuid-here
COOKIECLOUD_PASSWORD=your-password-here
```

### Optional Configuration

```bash
# Application Settings
APP_ENV=production
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false
LOG_LEVEL=INFO
API_KEY=your-api-key  # Optional API key authentication

# Xingtu API Settings
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RATE_LIMIT_PER_MINUTE=60

# Export Settings
EXPORT_DIR=exports
MAX_EXPORT_SIZE_MB=100
EXCEL_FORMAT=xlsx
```

## 📚 API Documentation

### Core Endpoints

#### Get Author Information
```http
GET /author/{author_id}
```

**Parameters:**
- `author_id` (required): Xingtu author ID
- `endpoints` (optional): Comma-separated list of specific endpoints
- `category` (optional): Filter by category (basic, commerce, analytics, etc.)

**Example:**
```bash
curl "http://localhost:8000/author/1798563067188323?category=basic"
```

#### Export Author Data
```http
POST /export
```

**Request Body:**
```json
{
  "author_id": "1798563067188323",
  "format": "excel",
  "include_raw_data": true,
  "include_summary": true,
  "filename": "custom_filename"
}
```

#### Batch Processing
```http
POST /batch
```

**Request Body:**
```json
{
  "author_ids": ["1798563067188323", "another_id"],
  "format": "excel",
  "max_concurrent": 5
}
```

#### Health Check
```http
GET /health
```

### Available Categories

- `basic`: Basic author information
- `commerce`: Commerce and sales data
- `content`: Content and video information
- `analytics`: Analytics and performance data
- `marketing`: Marketing information
- `contract`: Contract information

### Available Endpoints

The API provides access to 25+ Xingtu endpoints including:

- Author base information
- Commerce seed data
- Content analytics
- Audience distribution
- Video performance
- Sales metrics
- And more...

Get the complete list:
```http
GET /endpoints
```

## 🔧 Usage Examples

### Python Client Example

```python
import httpx
import asyncio

async def fetch_author_data():
    async with httpx.AsyncClient() as client:
        # Get basic author info
        response = await client.get(
            "http://localhost:8000/author/1798563067188323?category=basic"
        )
        data = response.json()
        print(f"Author: {data.get('base_info', {}).get('data', {}).get('nickname', 'Unknown')}")
        
        # Export to Excel
        export_response = await client.post(
            "http://localhost:8000/export",
            json={
                "author_id": "1798563067188323",
                "format": "excel",
                "include_summary": True
            }
        )
        
        if export_response.status_code == 200:
            with open("author_data.xlsx", "wb") as f:
                f.write(export_response.content)
            print("Excel file saved!")

asyncio.run(fetch_author_data())
```

### cURL Examples

```bash
# Get all author data
curl "http://localhost:8000/author/1798563067188323"

# Get specific endpoints
curl "http://localhost:8000/author/1798563067188323?endpoints=base_info,global_info"

# Export to JSON
curl -X POST "http://localhost:8000/export" \
  -H "Content-Type: application/json" \
  -d '{"author_id": "1798563067188323", "format": "json"}'

# Health check
curl "http://localhost:8000/health"
```

## 🧪 Testing

### Run Unit Tests
```bash
pytest tests/test_api.py -v
```

### Run Integration Tests
```bash
# Requires CookieCloud configuration
export COOKIECLOUD_SERVER_URL="your-server"
export COOKIECLOUD_UUID="your-uuid"
export COOKIECLOUD_PASSWORD="your-password"
export TEST_AUTHOR_ID="1798563067188323"

pytest tests/test_integration.py -v
```

### Test with Real Data
```bash
python tests/test_integration.py
```

## 📊 Monitoring

### Health Checks

The application provides comprehensive health checks:

```bash
curl http://localhost:8000/health
```

Response includes:
- Cookie manager status
- Xingtu client health
- Export system status
- Component details

### Logging

Structured logging with multiple levels:
- Application logs: `logs/xingtu_api_YYYYMMDD.log`
- Request logs: Console and file output
- Performance metrics: Response times and success rates

### Metrics

Monitor key metrics:
- Request success rates
- Response times
- Cookie refresh status
- Export operation statistics

## 🔒 Security

### Authentication

- Optional API key authentication
- Secure cookie management via CookieCloud
- No hardcoded credentials

### Rate Limiting

- Configurable request limits
- Automatic retry with backoff
- Protection against abuse

### Data Protection

- Sensitive data not logged
- Secure cookie transmission
- Input validation and sanitization

## 🚀 Production Deployment

### Docker Compose (Recommended)

```bash
# Production deployment with Nginx
docker-compose --profile production up -d
```

### Environment Setup

1. **Configure CookieCloud**: Set up CookieCloud server with Xingtu cookies
2. **Set Environment Variables**: Configure all required variables
3. **SSL/TLS**: Configure HTTPS in Nginx (uncomment SSL section)
4. **Monitoring**: Set up log aggregation and monitoring
5. **Backup**: Configure export directory backup

### Scaling

- Horizontal scaling: Run multiple container instances
- Load balancing: Use Nginx upstream configuration
- Caching: Enable Redis profile for response caching
- Database: Add persistent storage for analytics

## 🛠️ Development

### Project Structure

```
星图超级接口线上化/
├── config/              # Configuration management
├── core/                # Core business logic
├── utils/               # Utility functions
├── tests/               # Test suites
├── nginx/               # Nginx configuration
├── main.py              # FastAPI application
├── requirements.txt     # Python dependencies
├── Dockerfile           # Container configuration
├── docker-compose.yml   # Multi-container setup
└── README.md           # Documentation
```

### Adding New Features

1. **New API Endpoints**: Add to `config/api_endpoints.py`
2. **New Export Formats**: Extend `core/excel_exporter.py`
3. **New Middleware**: Add to `main.py`
4. **New Tests**: Add to `tests/` directory

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the test files for examples

## 🔄 Updates

The application automatically handles:
- Cookie refresh cycles
- API endpoint updates
- Error recovery
- Health monitoring

Regular maintenance:
- Monitor logs for errors
- Update dependencies
- Refresh CookieCloud configuration
- Backup export data
