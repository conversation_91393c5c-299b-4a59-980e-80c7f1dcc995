#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration settings for 星图超级接口线上化
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field, validator
from pathlib import Path


class CookieCloudConfig(BaseSettings):
    """CookieCloud configuration"""
    server_url: str = Field(..., env="COOKIECLOUD_SERVER_URL")
    uuid: str = Field(..., env="COOKIECLOUD_UUID")
    password: str = Field(..., env="COOKIECLOUD_PASSWORD")

    class Config:
        env_prefix = "COOKIECLOUD_"


class XingtuConfig(BaseSettings):
    """Xingtu API configuration"""
    base_url: str = Field("https://www.xingtu.cn", env="XINGTU_BASE_URL")
    domain: str = Field("www.xingtu.cn", env="XINGTU_DOMAIN")
    timeout: int = Field(30, env="REQUEST_TIMEOUT")
    max_retries: int = Field(3, env="MAX_RETRIES")
    retry_interval: int = Field(2, env="RETRY_INTERVAL")
    rate_limit_per_minute: int = Field(60, env="RATE_LIMIT_PER_MINUTE")

    class Config:
        env_prefix = "XINGTU_"


class AppConfig(BaseSettings):
    """Application configuration"""
    env: str = Field("development", env="APP_ENV")
    host: str = Field("0.0.0.0", env="API_HOST")
    port: int = Field(8000, env="API_PORT")
    debug: bool = Field(True, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    api_key: Optional[str] = Field(None, env="API_KEY")
    cors_origins: str = Field("*", env="CORS_ORIGINS")
    enable_request_logging: bool = Field(True, env="ENABLE_REQUEST_LOGGING")

    @validator("cors_origins")
    def parse_cors_origins(cls, v):
        if v == "*":
            return ["*"]
        return [origin.strip() for origin in v.split(",")]

    class Config:
        env_prefix = "APP_"


class ExportConfig(BaseSettings):
    """Export configuration"""
    export_dir: str = Field("exports", env="EXPORT_DIR")
    max_export_size_mb: int = Field(100, env="MAX_EXPORT_SIZE_MB")
    excel_format: str = Field("xlsx", env="EXCEL_FORMAT")

    @validator("export_dir")
    def create_export_dir(cls, v):
        Path(v).mkdir(parents=True, exist_ok=True)
        return v

    class Config:
        env_prefix = "EXPORT_"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration"""
    health_check_interval: int = Field(60, env="HEALTH_CHECK_INTERVAL")
    cookie_refresh_interval: int = Field(30, env="COOKIE_REFRESH_INTERVAL")

    class Config:
        env_prefix = "MONITORING_"


class Settings(BaseSettings):
    """Main application settings"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.cookiecloud = CookieCloudConfig()
        self.xingtu = XingtuConfig()
        self.app = AppConfig()
        self.export = ExportConfig()
        self.monitoring = MonitoringConfig()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Validate required environment variables
def validate_settings():
    """Validate that all required settings are present"""
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID", 
        "COOKIECLOUD_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True


# Common HTTP headers for Xingtu requests
XINGTU_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8'
}
