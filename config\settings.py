#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration settings for 星图超级接口线上化
"""

import os
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pathlib import Path


class CookieCloudConfig(BaseSettings):
    """CookieCloud configuration"""
    server_url: str = Field(default="http://localhost:8088", env="COOKIECLOUD_SERVER_URL")
    uuid: str = Field(default="your-uuid-here", env="COOKIECLOUD_UUID")
    password: str = Field(default="your-password-here", env="COOKIECLOUD_PASSWORD")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class XingtuConfig(BaseSettings):
    """Xingtu API configuration"""
    base_url: str = Field(default="https://www.xingtu.cn", env="XINGTU_BASE_URL")
    domain: str = Field(default="www.xingtu.cn", env="XINGTU_DOMAIN")
    timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    retry_interval: int = Field(default=2, env="RETRY_INTERVAL")
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class AppConfig(BaseSettings):
    """Application configuration"""
    env: str = Field(default="development", env="APP_ENV")
    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    cors_origins: str = Field(default="*", env="CORS_ORIGINS")
    enable_request_logging: bool = Field(default=True, env="ENABLE_REQUEST_LOGGING")

    @validator("cors_origins")
    def parse_cors_origins(cls, v):
        if v == "*":
            return ["*"]
        return [origin.strip() for origin in v.split(",")]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class ExportConfig(BaseSettings):
    """Export configuration"""
    export_dir: str = Field(default="exports", env="EXPORT_DIR")
    max_export_size_mb: int = Field(default=100, env="MAX_EXPORT_SIZE_MB")
    excel_format: str = Field(default="xlsx", env="EXCEL_FORMAT")

    @validator("export_dir")
    def create_export_dir(cls, v):
        Path(v).mkdir(parents=True, exist_ok=True)
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration"""
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    cookie_refresh_interval: int = Field(default=30, env="COOKIE_REFRESH_INTERVAL")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


class Settings:
    """Main application settings"""
    
    def __init__(self, **kwargs):
        self.cookiecloud = CookieCloudConfig()
        self.xingtu = XingtuConfig()
        self.app = AppConfig()
        self.export = ExportConfig()
        self.monitoring = MonitoringConfig()


# Global settings instance
settings = Settings()


# Validate required environment variables
def validate_settings():
    """Validate that all required settings are present"""
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID", 
        "COOKIECLOUD_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True


# Common HTTP headers for Xingtu requests
XINGTU_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8'
}
