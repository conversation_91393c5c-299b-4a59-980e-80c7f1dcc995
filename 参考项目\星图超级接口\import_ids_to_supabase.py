import os
import sys
import time
import json
from supabase import create_client, Client

# Supabase 配置
SUPABASE_URL = "https://avfjtwlcqrobvrmytkde.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2Zmp0d2xjcXJvYnZybXl0a2RlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjYwNjYyOCwiZXhwIjoyMDYyMTgyNjI4fQ.RKPBGCd7npnapF-_gTpMdBhsWwYAt0rXtUIx6EM3Q98"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# 小红书平台ID (从平台表获取)
XIAOHONGSHU_PLATFORM_ID = 1  # 假设小红书的platform_id为1，根据实际情况调整

def read_author_ids_from_file(file_path):
    """从文本文件中读取达人ID，每行一个ID"""
    author_ids = []
    if not os.path.exists(file_path):
        print(f"错误: 文件未找到 {file_path}")
        return author_ids
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                author_id = line.strip()
                # 基本验证: 检查是否是数字且不为空
                if author_id.isdigit():
                    author_ids.append(author_id)
                elif author_id:  # 记录非空但非纯数字的行
                    print(f"警告: 跳过无效行 '{author_id}' (非纯数字)")
        
        # 去除重复并保持顺序
        author_ids = list(dict.fromkeys(author_ids))
        print(f"从 {file_path} 读取到 {len(author_ids)} 个有效达人ID.")
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {e}")
    
    return author_ids

def get_existing_kol_ids():
    """获取已存在于数据库中的达人ID列表"""
    try:
        response = supabase.table("kol_data_update").select("kol_id").execute()
        existing_ids = [item["kol_id"] for item in response.data]
        print(f"数据库中已有 {len(existing_ids)} 个达人ID记录")
        return existing_ids
    except Exception as e:
        print(f"从数据库获取现有达人ID时出错: {e}")
        return []

def import_ids_to_supabase(author_ids, batch_size=50):
    """将达人ID批量导入到Supabase"""
    if not author_ids:
        print("没有有效的达人ID可导入")
        return
    
    # 获取已存在的达人ID
    existing_ids = get_existing_kol_ids()
    
    # 过滤掉已存在的ID
    new_ids = [id for id in author_ids if id not in existing_ids]
    
    if not new_ids:
        print("所有达人ID已存在于数据库中，无需导入")
        return
    
    print(f"开始导入 {len(new_ids)} 个新达人ID...")
    
    # 准备批量插入数据
    total_new_ids = len(new_ids)
    inserted_count = 0
    error_count = 0
    
    # 分批处理
    for i in range(0, total_new_ids, batch_size):
        batch = new_ids[i:i+batch_size]
        batch_data = [
            {
                "kol_id": kol_id,
                "platform_id": XIAOHONGSHU_PLATFORM_ID,
                "data_status": "pending"
            } for kol_id in batch
        ]
        
        try:
            response = supabase.table("kol_data_update").insert(batch_data).execute()
            inserted_this_batch = len(response.data)
            inserted_count += inserted_this_batch
            print(f"已导入 {inserted_count}/{total_new_ids} 个达人ID")
        except Exception as e:
            error_count += len(batch)
            print(f"导入批次 {i//batch_size + 1} 时出错: {e}")
        
        # 短暂暂停，避免请求过快
        if i + batch_size < total_new_ids:
            time.sleep(0.5)
    
    print(f"\n导入完成! 成功: {inserted_count}, 失败: {error_count}")

def main():
    file_path = "d:\\010101010101\\星图超级接口\\new.txt"
    
    print("开始从文件导入达人ID到Supabase...")
    author_ids = read_author_ids_from_file(file_path)
    
    if author_ids:
        import_ids_to_supabase(author_ids)
    else:
        print("没有找到有效的达人ID，导入终止")

if __name__ == "__main__":
    main()
