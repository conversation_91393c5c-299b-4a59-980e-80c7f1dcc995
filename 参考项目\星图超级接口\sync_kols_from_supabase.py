import requests
import warnings
import urllib3
import json
import time
import pandas as pd
import os
import sys
from supabase import create_client, Client

# 忽略不安全请求警告
warnings.simplefilter('ignore', urllib3.exceptions.InsecureRequestWarning)
warnings.simplefilter('ignore', requests.packages.urllib3.exceptions.InsecureRequestWarning)

# --- Supabase配置 ---
SUPABASE_URL = "https://avfjtwlcqrobvrmytkde.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF2Zmp0d2xjcXJvYnZybXl0a2RlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjYwNjYyOCwiZXhwIjoyMDYyMTgyNjI4fQ.RKPBGCd7npnapF-_gTpMdBhsWwYAt0rXtUIx6EM3Q98"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# --- 星图接口配置 ---
BASE_URL = "https://www.xingtu.cn"
COOKIE = "s_v_web_id=verify_m9qxjubu_HLVFes74_oVPh_42lW_Ae85_WjD5hF136EVd; csrf_session_id=1111e0d59d2dbded2c7ff8d0ed7010c8; tt_webid=7495710578595333659; csrftoken=3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I; passport_csrf_token=24e5abbc2ac713c15a146cf11328edef; passport_csrf_token_default=24e5abbc2ac713c15a146cf11328edef; passport_auth_status=f54ae30d80da739a44705016bd08cacc%2C; passport_auth_status_ss=f54ae30d80da739a44705016bd08cacc%2C; sid_guard=d17823b9568a5e4d638054f07a24b406%7C1745231145%7C5184001%7CFri%2C+20-Jun-2025+10%3A25%3A46+GMT; uid_tt=b872dc5d9797ca9d395ada8525b9d668; uid_tt_ss=b872dc5d9797ca9d395ada8525b9d668; sid_tt=d17823b9568a5e4d638054f07a24b406; sessionid=d17823b9568a5e4d638054f07a24b406; sessionid_ss=d17823b9568a5e4d638054f07a24b406; is_staff_user=false; sid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; ssid_ucp_v1=1.0.0-KDliNThkODdhNWI0ZDE2NWMyNTg2YTUwYTI1NmU3Nzc4NmNkNWEwNGIKFwjqgKDIvMzdAhCpupjABhimDDgBQOsHGgJsZiIgZDE3ODIzYjk1NjhhNWU0ZDYzODA1NGYwN2EyNGI0MDY; star_sessionid=d17823b9568a5e4d638054f07a24b406; Hm_lvt_5d77c979053345c4bd8db63329f818ec=**********; Hm_lpvt_5d77c979053345c4bd8db63329f818ec=**********; HMACCOUNT=E102D4CB3F9EAFEA"
CSRF_TOKEN = "3x3kLg64-RpKWXsTn5N6IS8-mFEIWih5WC_I"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'sec-ch-ua-platform': '"Windows"',
    'x-csrftoken': CSRF_TOKEN,
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
    'Cookie': COOKIE
}

# API端点配置
API_ENDPOINTS = [
    {"name": "commerce_seed_base_info", "path": "/gw/api/aggregator/get_author_commerce_seed_base_info", "specific_params": {"range": "30"}, "id_param_name": "o_author_id"},
    {"name": "commerce_spread_info", "path": "/gw/api/aggregator/get_author_commerce_spread_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "content_label_density", "path": "/gw/api/aggregator/get_author_content_label_density", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "contract_base_info", "path": "/gw/api/aggregator/get_author_contract_base_info", "specific_params": {"range": "90"}, "id_param_name": "o_author_id"},
    {"name": "global_info", "path": "/gw/api/aggregator/get_author_global_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "homepage_videos", "path": "/gw/api/aggregator/get_author_homepage_videos", "specific_params": {"page": "1", "limit": "10"}, "id_param_name": "o_author_id"},
    {"name": "order_experience", "path": "/gw/api/aggregator/get_author_order_experience", "specific_params": {"period": "30"}, "id_param_name": "o_author_id"},
    {"name": "side_base_info", "path": "/gw/api/aggregator/get_author_side_base_info", "specific_params": {}, "id_param_name": "o_author_id"},
    {"name": "video_live_linkage_product_list", "path": "/gw/api/aggregator/get_author_video_live_linkage_product_list", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "video_live_linkage_stat", "path": "/gw/api/aggregator/get_author_video_live_linkage_stat", "specific_params": {"time_period": "30"}, "id_param_name": "star_author_id"},
    {"name": "base_info", "path": "/gw/api/author/get_author_base_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true"}, "id_param_name": "o_author_id"},
    {"name": "marketing_info", "path": "/gw/api/author/get_author_marketing_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "platform_channel_info_v2", "path": "/gw/api/author/get_author_platform_channel_info_v2", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "show_items_v2", "path": "/gw/api/author/get_author_show_items_v2", "specific_params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "hot_comment_tokens", "path": "/gw/api/data/get_author_hot_comment_tokens", "specific_params": {"num": "10", "without_emoji": "true"}, "id_param_name": "author_id"},
    {"name": "audience_distribution", "path": "/gw/api/data_sp/author_audience_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1", "link_type": "5"}, "id_param_name": "o_author_id"},
    {"name": "cp_info", "path": "/gw/api/data_sp/author_cp_info", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "daily_link", "path": "/gw/api/data_sp/author_daily_link", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22"}, "id_param_name": "o_author_id"},
    {"name": "link_card", "path": "/gw/api/data_sp/author_link_card", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "link_struct", "path": "/gw/api/data_sp/author_link_struct", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "local_info", "path": "/gw/api/data_sp/author_local_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30"}, "id_param_name": "o_author_id"},
    {"name": "local_sales_performance", "path": "/gw/api/data_sp/author_local_sales_performance", "specific_params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0"}, "id_param_name": "o_author_id"},
    {"name": "shopping_videos", "path": "/gw/api/data_sp/author_shopping_videos", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0"}, "id_param_name": "o_author_id"},
    {"name": "spread_videos", "path": "/gw/api/data_sp/author_spread_videos", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "video_distribution", "path": "/gw/api/data_sp/author_video_distribution", "specific_params": {"platform_source": "1", "platform_channel": "1"}, "id_param_name": "o_author_id"},
    {"name": "convert_ability", "path": "/gw/api/data_sp/get_author_convert_ability", "specific_params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2"}, "id_param_name": "o_author_id"},
    {"name": "daily_fans", "path": "/gw/api/data_sp/get_author_daily_fans", "specific_params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1"}, "id_param_name": "author_id"},
    {"name": "spread_info_data_sp", "path": "/gw/api/data_sp/get_author_spread_info", "specific_params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2"}, "id_param_name": "o_author_id"},
    {"name": "business_card_info", "path": "/gw/api/gauthor/author_get_business_card_info", "specific_params": {}, "id_param_name": "o_author_id"},
]

PROXIES = {
  "http": None,
  "https": None,
}

# --- 函数 ---

def test_cookie_validity():
    """测试Cookie有效性"""
    print("正在测试Cookie有效性...")
    test_session = requests.Session()
    test_session.headers.update(HEADERS)
    test_session.proxies.update(PROXIES)
    test_session.verify = False
    
    # 选择一个简单的端点进行测试
    test_endpoint = {"name": "global_info", "path": "/gw/api/aggregator/get_author_global_info", "specific_params": {}, "id_param_name": "o_author_id"}
    test_author_id = "7289149524013121594"  # 使用一个可能存在的测试ID
    
    try:
        print(f"测试端点: {test_endpoint['name']}, 测试作者ID: {test_author_id}")
        url = BASE_URL + test_endpoint["path"]
        query_params = test_endpoint["specific_params"].copy()
        query_params[test_endpoint["id_param_name"]] = test_author_id
        
        response = test_session.get(url, params=query_params, timeout=20)
        
        if response.status_code != 200:
            print(f"警告: HTTP状态码 {response.status_code} - Cookie可能无效")
            print(f"响应内容: {response.text[:200]}...")
            return False
        else:
            data = response.json()
            if data.get("code") == 0 or data.get("status_code") == 0 or data.get("base_resp", {}).get("status_code") == 0:
                print("✓ Cookie有效! API请求成功")
                return True
            else:
                error_code = data.get('code', data.get('status_code', data.get('base_resp', {}).get('status_code', 'N/A')))
                error_message = data.get('message', data.get('status_message', 'N/A'))
                print(f"× Cookie可能无效! 错误代码: {error_code}, 错误信息: {error_message}")
                print(f"响应内容: {json.dumps(data, ensure_ascii=False)[:200]}...")
                return False
    except Exception as e:
        print(f"× 测试Cookie时发生错误: {e}")
        return False

def fetch_single_endpoint(session, endpoint_config, o_author_id):
    """从单个星图API端点获取数据。返回端点名称和数据字符串（JSON或错误）。"""
    url = BASE_URL + endpoint_config["path"]
    query_params = endpoint_config["specific_params"].copy()
    query_params[endpoint_config["id_param_name"]] = o_author_id
    endpoint_name = endpoint_config["name"]

    data_string = None  # 默认为None
    print(f"  获取 {endpoint_name}...")

    try:
        response = session.get(url, params=query_params, timeout=20)

        if response.status_code != 200:
            print(f"    HTTP错误 {endpoint_name}: 状态码 {response.status_code}")
            error_message = f"HTTP错误: 状态码 {response.status_code}"
            try:
                content_snippet = response.text[:500]
            except Exception:
                content_snippet = "(无法读取响应内容)"
            data_string = json.dumps({"error": error_message, "status": "http_error", "content_snippet": content_snippet}, ensure_ascii=False, indent=2)
            return endpoint_name, data_string

        try:
            data = response.json()
            is_success = (
                data.get("code") == 0 or
                data.get("status_code") == 0 or
                data.get("base_resp", {}).get("status_code") == 0
            )

            if is_success:
                extracted_data = data.get("data", data)
                data_string = json.dumps(extracted_data, ensure_ascii=False, indent=2)
                print(f"    {endpoint_name} 获取成功.")
            else:
                error_code = data.get('code', data.get('status_code', data.get('base_resp', {}).get('status_code', 'N/A')))
                error_message = data.get('message', data.get('status_message', 'N/A'))
                api_error_message = f"API错误: code={error_code}, message={error_message}"
                data_string = json.dumps({"error": api_error_message, "status": "api_error", "raw_response": data}, ensure_ascii=False, indent=2)
                print(f"    API错误 {endpoint_name}: {error_message} (代码: {error_code})")

        except json.JSONDecodeError as e:
            print(f"    警告: JSON解析错误 {endpoint_name}. 内容: {response.text[:200]}...")
            error_message = f"JSON解析错误: {e}"
            data_string = json.dumps({"error": error_message, "status": "json_decode_error", "content_snippet": response.text[:500]}, ensure_ascii=False, indent=2)

    except requests.exceptions.RequestException as e:
        error_message = f"请求失败: {str(e)}"
        data_string = json.dumps({"error": error_message, "status": "request_error"}, ensure_ascii=False, indent=2)
        print(f"    请求错误 {endpoint_name}: {e}")
    except Exception as e:
        error_message = f"处理失败: {str(e)}"
        data_string = json.dumps({"error": error_message, "status": "processing_error"}, ensure_ascii=False, indent=2)
        print(f"    处理 {endpoint_name} 时发生意外错误: {e}")

    return endpoint_name, data_string  # 返回名称和JSON字符串（数据或错误）

def get_pending_kol_ids(limit=50, offset=0):
    """从Supabase获取待处理的达人ID
    
    Args:
        limit: 每次获取的最大记录数
        offset: 分页偏移量，用于断点续传
    """
    try:
        # 获取指定状态的记录，按ID排序确保处理顺序一致性
        response = supabase.table("kol_data_update") \
            .select("id,kol_id") \
            .eq("data_status", "pending") \
            .order("id") \
            .range(offset, offset + limit - 1) \
            .execute()
        
        kol_records = response.data
        if kol_records:
            print(f"获取到 {len(kol_records)} 个待处理的达人记录 (偏移量: {offset})")
        else:
            print(f"未找到待处理的达人记录 (偏移量: {offset})")
        
        return kol_records
    except Exception as e:
        print(f"获取待处理达人ID时出错: {e}")
        return []

def update_kol_status(kol_db_id, status, data_json=None, retry_count=3):
    """更新达人在数据库中的状态和数据
    
    Args:
        kol_db_id: 数据库中的记录ID
        status: 新状态 (pending, processing, completed, failed)
        data_json: 要保存的数据
        retry_count: 重试次数
        
    Returns:
        bool: 是否更新成功
    """
    for attempt in range(retry_count):
        try:
            # 准备要更新的数据
            update_data = {
                "data_status": status,
                "updated_at": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            if data_json is not None:
                update_data["data_json"] = data_json
            
            # 仅当记录处于预期状态时才更新，避免并发冲突
            expected_status = "pending"
            if status == "completed" or status == "failed":
                expected_status = "processing"
                
            response = supabase.table("kol_data_update") \
                .update(update_data) \
                .eq("id", kol_db_id) \
                .eq("data_status", expected_status) \
                .execute()
            
            # 检查是否成功更新
            if response.data and len(response.data) > 0:
                print(f"  成功更新达人ID {kol_db_id} 的状态为 {status}")
                return True
            else:
                # 如果无法更新，检查记录当前状态
                check_resp = supabase.table("kol_data_update").select("data_status").eq("id", kol_db_id).execute()
                if check_resp.data and len(check_resp.data) > 0:
                    current_status = check_resp.data[0].get("data_status")
                    print(f"  无法更新达人ID {kol_db_id}，当前状态为 {current_status}，预期状态为 {expected_status}")
                    # 如果状态已经是完成或失败，记为成功
                    if current_status in ["completed", "failed"] and status in ["completed", "failed"]:
                        return True
                else:
                    print(f"  记录ID {kol_db_id} 不存在")
                
                if attempt < retry_count - 1:
                    wait_time = 0.5 * (attempt + 1)  # 逐次增加等待时间
                    print(f"  等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
        except Exception as e:
            print(f"  更新达人状态时出错 (ID: {kol_db_id}): {e}")
            if attempt < retry_count - 1:
                print(f"  将在 {0.5 * (attempt + 1)} 秒后重试...")
                time.sleep(0.5 * (attempt + 1))
    
    print(f"  经过 {retry_count} 次尝试，无法更新达人ID {kol_db_id} 的状态")
    return False

def process_kol_data(session, kol_record):
    """处理单个达人的数据
    
    Args:
        session: 请求会话
        kol_record: 达人记录字典
        
    Returns:
        bool: 是否成功处理
    """
    kol_db_id = kol_record["id"]
    kol_id = kol_record["kol_id"]
    
    print(f"\n--- 处理达人 (ID: {kol_id}, DB_ID: {kol_db_id}) ---")
    
    # 检查记录当前状态，确保它仍然是pending
    try:
        check_resp = supabase.table("kol_data_update").select("data_status").eq("id", kol_db_id).execute()
        if not check_resp.data or len(check_resp.data) == 0:
            print(f"  错误: 记录ID {kol_db_id} 不存在，跳过处理")
            return False
            
        current_status = check_resp.data[0].get("data_status")
        if current_status != "pending":
            print(f"  跳过: 记录ID {kol_db_id} 当前状态为 {current_status}(非待处理)")
            return True  # 跳过但算作成功处理，不打断整体流程
    except Exception as e:
        print(f"  错误: 检查记录状态时出错: {e}")
        return False
    
    # 更新状态为处理中
    if not update_kol_status(kol_db_id, "processing"):
        print(f"  无法将达人 {kol_id} 状态更新为处理中，跳过处理")
        return False
    
    # 用于存储所有端点数据的字典
    all_data = {}
    success_endpoints = 0
    failed_endpoints = 0
    skipped_endpoints = 0
    
    try:
        # 获取所有端点数据，单个端点错误不影响整体运行
        for i, endpoint_config in enumerate(API_ENDPOINTS):
            try:
                endpoint_name, data_str = fetch_single_endpoint(session, endpoint_config, kol_id)
                
                # 检查数据是否包含错误
                contains_error = False
                if data_str is not None:
                    try:
                        data_obj = json.loads(data_str)
                        if isinstance(data_obj, dict) and "error" in data_obj:
                            contains_error = True
                            failed_endpoints += 1
                    except json.JSONDecodeError:
                        contains_error = True
                        failed_endpoints += 1
                else:
                    contains_error = True
                    skipped_endpoints += 1
                
                if not contains_error:
                    success_endpoints += 1
                    
                # 存储结果（JSON字符串或错误 JSON字符串）
                all_data[endpoint_name] = data_str if data_str is not None else json.dumps(
                    {"error": "No data returned", "status": "empty_response"}, ensure_ascii=False, indent=2
                )
                
                # 每10个端点保存一次中间结果，避免全部失败
                if (i + 1) % 10 == 0 and i > 0:
                    temp_combined_data = {
                        "kol_id": kol_id,
                        "fetch_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "fetch_progress": f"{i+1}/{len(API_ENDPOINTS)}",
                        "endpoints": all_data,
                        "stats": {
                            "success": success_endpoints,
                            "failed": failed_endpoints,
                            "skipped": skipped_endpoints,
                            "total": i+1
                        }
                    }
                    # 保存中间状态，但不改变processing状态
                    supabase.table("kol_data_update").update({"data_json": temp_combined_data}).eq("id", kol_db_id).execute()
                    print(f"  已保存中间进度: {i+1}/{len(API_ENDPOINTS)} 端点")
                
            except Exception as e:
                print(f"  处理端点 {endpoint_config['name']} 时出错: {e}")
                # 记录错误但继续处理其他端点
                all_data[endpoint_config['name']] = json.dumps(
                    {"error": f"Processing error: {str(e)}", "status": "processing_error"}, 
                    ensure_ascii=False, indent=2
                )
                failed_endpoints += 1
            
            # 大多数端点终端短暂暂停，每10个端点休息稍长一些
            sleep_time = 0.1
            if (i + 1) % 10 == 0:
                sleep_time = 0.5  # 每10个端点休息时间稍长
            print(f"  等待 {sleep_time} 秒...")
            time.sleep(sleep_time)
        
        # 将所有数据合并为一个JSON对象
        combined_data = {
            "kol_id": kol_id,
            "fetch_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "endpoints": all_data,
            "stats": {
                "success": success_endpoints,
                "failed": failed_endpoints,
                "skipped": skipped_endpoints,
                "total": len(API_ENDPOINTS)
            }
        }
        
        # 判断处理是否比较成功
        final_status = "completed"
        success_ratio = success_endpoints / len(API_ENDPOINTS) if len(API_ENDPOINTS) > 0 else 0
        
        if success_ratio < 0.3:  # 如果成功率低于30%，则认为是失败
            print(f"  达人 {kol_id} 数据获取失败率过高 (仅 {success_ratio:.0%} 成功)")
            final_status = "failed"
        
        # 更新状态为已完成或失败，并保存数据
        update_success = update_kol_status(kol_db_id, final_status, combined_data)
        if update_success:
            print(f"  达人 {kol_id} 数据获取结果: 成功 {success_endpoints}, 失败 {failed_endpoints}, 跳过 {skipped_endpoints}")
        else:
            print(f"  达人 {kol_id} 数据最终保存失败")
        
        return update_success
    
    except Exception as e:
        print(f"  处理达人 {kol_id} 时发生未处理错误: {e}")
        error_data = {
            "kol_id": kol_id,
            "fetch_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "error": str(e),
            "endpoints": all_data,  # 已获取的数据仍然保存
            "stats": {
                "success": success_endpoints,
                "failed": failed_endpoints,
                "skipped": skipped_endpoints,
                "total": len(API_ENDPOINTS),
                "error": str(e)
            }
        }
        # 更新状态为失败，但保存已获取的数据
        update_kol_status(kol_db_id, "failed", error_data)
        return False

def get_progress_file_path():
    """返回进度文件路径"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), "sync_progress.json")

def save_progress(offset, batch_size, processed_count, success_count, failed_count, total_found=None):
    """保存进度到文件"""
    progress_data = {
        "last_run": time.strftime("%Y-%m-%d %H:%M:%S"),
        "offset": offset,
        "batch_size": batch_size,
        "processed_count": processed_count,
        "success_count": success_count,
        "failed_count": failed_count,
        "total_found": total_found
    }
    
    try:
        with open(get_progress_file_path(), "w", encoding="utf-8") as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
        print(f"\n进度已保存到 {get_progress_file_path()}")
    except Exception as e:
        print(f"\n保存进度时出错: {e}")

def load_progress():
    """从文件加载进度"""
    progress_file = get_progress_file_path()
    if not os.path.exists(progress_file):
        return None
    
    try:
        with open(progress_file, "r", encoding="utf-8") as f:
            progress_data = json.load(f)
        print(f"\n已加载上次进度: 当前偏移量 {progress_data['offset']}, 已处理 {progress_data['processed_count']} 个达人")
        return progress_data
    except Exception as e:
        print(f"\n警告: 进度文件加载出错: {e}")
        return None
    
def get_total_pending_count():
    """获取总共待处理的记录数"""
    try:
        response = supabase.table("kol_data_update") \
            .select("count", count="exact") \
            .eq("data_status", "pending") \
            .execute()
        count = response.count if hasattr(response, 'count') else 0
        return count
    except Exception as e:
        print(f"获取待处理记录总数时出错: {e}")
        return 0

def main(resume=True, batch_size=50, max_kols=None):
    """主函数
    
    Args:
        resume: 是否从上次中断的地方继续
        batch_size: 每批处理的达人数量
        max_kols: 最大处理达人数，为None则处理所有
    """
    print("开始同步Supabase中的达人数据...")
    
    # 测试Cookie有效性
    if not test_cookie_validity():
        print("Cookie无效，请更新后再试")
        return
    
    # 初始化统计和进度变量
    offset = 0
    processed_count = 0
    success_count = 0
    failed_count = 0
    
    # 获取总待处理数量
    total_pending = get_total_pending_count()
    print(f"总共有 {total_pending} 个待处理的达人记录")
    
    # 加载上次进度
    if resume:
        progress = load_progress()
        if progress:
            offset = progress.get("offset", 0)
            # 如果上次batch_size与当前不同，使用当前的
            batch_size = progress.get("batch_size", batch_size) 
            processed_count = progress.get("processed_count", 0)
            success_count = progress.get("success_count", 0)
            failed_count = progress.get("failed_count", 0)
    
    # 初始化会话
    session = requests.Session()
    session.headers.update(HEADERS)
    session.proxies.update(PROXIES)
    session.verify = False
    
    # 设置最大处理数量
    remaining = max_kols if max_kols is not None else float('inf')
    
    print(f"\n开始处理达人数据 (偏移量: {offset}, 批量: {batch_size})...")
    
    try:
        while remaining > 0:
            # 获取当前批次的达人
            batch_size_adjusted = min(batch_size, remaining)
            pending_kols = get_pending_kol_ids(limit=batch_size_adjusted, offset=offset)
            
            if not pending_kols:
                print(f"\n没有更多需要同步的达人，完成处理。")
                break
            
            # 处理每个达人
            for i, kol_record in enumerate(pending_kols):
                # 显示估计进度
                total_processed = processed_count + 1
                estimated_total = total_pending if total_pending > 0 else "?"
                progress_str = f"[{total_processed}/{estimated_total}]" if estimated_total != "?" else f"[{total_processed}/?]"
                
                print(f"\n{progress_str} 处理第 {processed_count + 1} 个达人 (当前批次: {i+1}/{len(pending_kols)})")
                
                # 处理达人数据
                start_time = time.time()
                success = process_kol_data(session, kol_record)
                elapsed_time = time.time() - start_time
                
                # 更新计数
                processed_count += 1
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                
                print(f"  处理耗时: {elapsed_time:.2f} 秒")
                print(f"  总进度: 成功 {success_count}, 失败 {failed_count}, 总计 {processed_count}")
                
                # 当处理批量的一半或每10个后保存进度
                if (i + 1) % max(10, batch_size // 2) == 0 or i == len(pending_kols) - 1:
                    save_progress(offset + i + 1, batch_size, processed_count, success_count, failed_count, total_pending)
                
                # 每个达人处理后等待一下，避免请求过快
                time.sleep(0.5)
                
                remaining -= 1
                if remaining <= 0:
                    break
            
            # 更新偏移量并保存进度
            offset += len(pending_kols)
            save_progress(offset, batch_size, processed_count, success_count, failed_count, total_pending)
            
            # 如果这一批次返回的数据少于请求的数据，说明没有更多数据了
            if len(pending_kols) < batch_size_adjusted:
                break
    
    except KeyboardInterrupt:
        print("\n用户中断程序运行！")
        save_progress(offset, batch_size, processed_count, success_count, failed_count, total_pending)
        print("\n进度已保存，可以稍后继续。")
        return
    except Exception as e:
        print(f"\n程序运行时发生错误: {e}")
        save_progress(offset, batch_size, processed_count, success_count, failed_count, total_pending)
        print("\n进度已保存，请修复错误后继续。")
        return
    
    # 清理进度文件（如果全部完成）
    if processed_count > 0 and offset >= total_pending:
        try:
            os.remove(get_progress_file_path())
            print("\n全部完成，进度文件已清理。")
        except:
            pass
    
    print(f"\n同步完成! 总计处理: {processed_count}, 成功: {success_count}, 失败: {failed_count}")

if __name__ == "__main__":
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="同步Supabase中的达人数据")
    parser.add_argument("--no-resume", action="store_true", help="不从上次中断的地方继续，从头开始处理")
    parser.add_argument("--batch-size", type=int, default=50, help="每批处理的达人数量，默认为50")
    parser.add_argument("--max", type=int, help="最大处理达人数，不设置则处理所有")
    parser.add_argument("--status", action="store_true", help="只显示当前进度和状态，不执行同步")
    
    args = parser.parse_args()
    
    # 如果只是显示状态
    if args.status:
        progress = load_progress()
        pending_count = get_total_pending_count()
        
        print("\n当前同步状态:")
        print(f"待处理达人数: {pending_count}")
        
        if progress:
            print(f"上次运行时间: {progress.get('last_run', '未知')}")
            print(f"当前偏移量: {progress.get('offset', 0)}")
            print(f"已处理达人数: {progress.get('processed_count', 0)}")
            print(f"成功处理数: {progress.get('success_count', 0)}")
            print(f"失败处理数: {progress.get('failed_count', 0)}")
            print(f"批处理大小: {progress.get('batch_size', 50)}")
            
            if pending_count > 0:
                remaining = pending_count - progress.get('offset', 0)
                if remaining > 0:
                    print(f"预计剩余达人数: {remaining}")
                else:
                    print("所有待处理达人已处理完成，可能有新的待处理达人被添加")
        else:
            print("未找到保存的进度信息，将从头开始处理")
            
        sys.exit(0)
    
    # 执行主程序
    main(
        resume=not args.no_resume, 
        batch_size=args.batch_size,
        max_kols=args.max
    )
